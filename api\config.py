"""
Configuration management for the FastAPI server
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    app_name: str = Field(default="UDCPR RAG Chatbot API", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o", env="OPENAI_MODEL")
    openai_embedding_model: str = Field(default="text-embedding-3-small", env="OPENAI_EMBEDDING_MODEL")
    openai_embedding_dimensions: int = Field(default=1024, env="OPENAI_EMBEDDING_DIMENSIONS")
    
    # Pinecone Configuration
    pinecone_api_key: str = Field(..., env="PINECONE_API_KEY")
    pinecone_environment: str = Field(default="us-east-1", env="PINECONE_ENVIRONMENT")
    pinecone_index_name: str = Field(default="udcpr-rag-index", env="PINECONE_INDEX_NAME")
    
    # RAG Configuration
    max_history_messages: int = Field(default=10, env="MAX_HISTORY_MESSAGES")
    top_k_results: int = Field(default=5, env="TOP_K_RESULTS")
    min_relevance_score: float = Field(default=0.5, env="MIN_RELEVANCE_SCORE")
    
    # Web Search Configuration
    enable_web_search: bool = Field(default=False, env="ENABLE_WEB_SEARCH")
    web_search_num_results: int = Field(default=3, env="WEB_SEARCH_NUM_RESULTS")
    
    # Supabase Configuration (Optional)
    supabase_url: Optional[str] = Field(default=None, env="SUPABASE_URL")
    supabase_api_key: Optional[str] = Field(default=None, env="SUPABASE_API_KEY")
    
    # CORS Configuration
    cors_origins: list = Field(default=["*"], env="CORS_ORIGINS")
    
    # Rate Limiting
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def validate_required_settings(self) -> bool:
        """Validate that all required settings are present"""
        required_fields = [
            "openai_api_key",
            "pinecone_api_key"
        ]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(self, field):
                missing_fields.append(field.upper())
        
        if missing_fields:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_fields)}")
        
        return True

    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return os.getenv("ENVIRONMENT", "development").lower() == "production"
    
    @property
    def supabase_enabled(self) -> bool:
        """Check if Supabase is configured"""
        return bool(self.supabase_url and self.supabase_api_key)

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    settings = Settings()
    settings.validate_required_settings()
    return settings

# Environment-specific configurations
def get_cors_origins() -> list:
    """Get CORS origins based on environment"""
    settings = get_settings()
    
    if settings.is_production:
        # In production, specify exact origins
        origins = os.getenv("CORS_ORIGINS", "").split(",")
        return [origin.strip() for origin in origins if origin.strip()]
    else:
        # In development, allow all origins
        return ["*"]

def get_log_config() -> dict:
    """Get logging configuration"""
    settings = get_settings()
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "root": {
            "level": settings.log_level,
            "handlers": ["default"],
        },
        "loggers": {
            "uvicorn": {
                "level": settings.log_level,
                "handlers": ["default"],
                "propagate": False,
            },
            "fastapi": {
                "level": settings.log_level,
                "handlers": ["default"],
                "propagate": False,
            },
        },
    }
