"""
Vercel-specific handler for the UDCPR RAG API
This file adapts the FastAPI app for Vercel's serverless environment
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set environment variables for Vercel
os.environ.setdefault("ENVIRONMENT", "production")
os.environ.setdefault("PYTHONPATH", ".")

try:
    from api_server import app
    
    # Vercel expects a handler function
    def handler(request, response):
        """Vercel serverless function handler"""
        return app(request, response)
    
    # Also export the app directly for ASGI
    application = app
    
except ImportError as e:
    print(f"Failed to import FastAPI app: {e}")
    
    # Fallback minimal handler
    def handler(request, response):
        return {
            "statusCode": 500,
            "body": f"Import error: {str(e)}"
        }
    
    # Minimal FastAPI app as fallback
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    
    application = FastAPI(title="UDCPR RAG API - Error")
    
    @application.get("/")
    async def root():
        return JSONResponse({
            "error": "Failed to initialize API",
            "message": "Check environment variables and dependencies"
        })
    
    @application.get("/api/health")
    async def health():
        return JSONResponse({
            "status": "error",
            "message": "API failed to initialize properly"
        })

# Export for Vercel
app = application
