# UDCPR RAG Chatbot API

🏗️ **Professional REST API for Maharashtra's Unified Development Control and Promotion Regulations (UDCPR)**

A FastAPI-based Retrieval Augmented Generation (RAG) system that provides intelligent question-answering capabilities for building regulations, zoning requirements, and development control rules in Maharashtra State, India.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/clumsypasta/API-UDCPR)
[![Deploy to Railway](https://railway.app/button.svg)](https://railway.app/template/fastapi)

## 🚀 Features

- **🤖 AI-Powered Responses**: Uses OpenAI GPT-4 for intelligent, context-aware answers
- **📚 Document Search**: Pinecone vector database for efficient similarity search
- **🌐 Web Search Integration**: Optional web search for additional context
- **💬 Chat History**: Maintains conversation context for better responses
- **🔍 Health Monitoring**: Built-in health checks and monitoring endpoints
- **📖 Interactive Documentation**: Auto-generated API documentation with Swagger UI
- **🔧 Easy Integration**: RESTful API that works with any frontend or application

## 🏗️ Architecture

```
Frontend (Any) → FastAPI Server → OpenAI GPT-4
                      ↓
                 Pinecone Vector DB
                      ↓
                 UDCPR Documents
```

## 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/chat` | Send messages to the chatbot |
| `POST` | `/api/search` | Search documents directly |
| `GET` | `/api/health` | Health check for all services |
| `GET` | `/docs` | Interactive API documentation |

## 🚀 Quick Start

### Local Development

1. **Clone the repository**
   ```bash
   git clone https://github.com/clumsypasta/API-UDCPR.git
   cd API-UDCPR
   ```

2. **Install dependencies**
   ```bash
   pip install -r api_requirements.txt
   ```

3. **Set environment variables**
   ```bash
   cp api.env.example .env
   # Edit .env with your API keys
   ```

4. **Start the server**
   ```bash
   python start_api.py
   ```

5. **Test the API**
   - Interactive docs: http://localhost:8000/docs
   - Health check: http://localhost:8000/api/health
   - Example frontend: Open `examples/html_example.html`

## 🌐 Deployment Options

### Option 1: Railway (Recommended)
```bash
# Push to GitHub, then connect to Railway
# Railway will auto-detect and deploy
```

### Option 2: Vercel (Limited Support)
```bash
# Connect GitHub repo to Vercel
# Set environment variables in Vercel dashboard
# Note: May have timeout limitations for AI processing
```

### Option 3: Render
```bash
# Connect GitHub repo to Render
# Uses render_api.yaml configuration
```

## 🔧 Environment Variables

### Required
```bash
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=udcpr-rag-index
```

### Optional
```bash
ENABLE_WEB_SEARCH=false
SUPABASE_URL=your_supabase_url
SUPABASE_API_KEY=your_supabase_key
OPENAI_MODEL=gpt-4o
```

## 💻 Frontend Integration

### JavaScript
```javascript
const response = await fetch('https://your-api.com/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ message: 'What are setback requirements?' })
});
const data = await response.json();
console.log(data.response);
```

### React Component
```jsx
import UDCPRChat from './examples/react_component.jsx';
<UDCPRChat apiBaseUrl="https://your-api.com" />
```

### cURL
```bash
curl -X POST "https://your-api.com/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What is FSI calculation?"}'
```

## 📁 Project Structure

```
├── api_server.py              # Main FastAPI application
├── api/
│   ├── models.py              # Request/response models
│   ├── config.py              # Configuration management
│   └── services/
│       ├── rag_service.py     # Core RAG functionality
│       └── legacy_integration.py
├── examples/
│   ├── javascript_integration.js
│   ├── react_component.jsx
│   └── html_example.html
├── api_requirements.txt       # Dependencies
├── vercel.json               # Vercel configuration
├── railway.toml              # Railway configuration
├── render_api.yaml           # Render configuration
└── Dockerfile                # Docker configuration
```

## 🤖 What You Can Ask

The API can answer questions about:
- **Building Regulations**: Height restrictions, setback requirements
- **Zoning Rules**: Residential, commercial, industrial zones
- **FSI Calculations**: Floor Space Index computations
- **Development Permissions**: Approval processes and requirements
- **Construction Guidelines**: Building codes and standards
- **Land Use**: Permitted uses in different zones

## 📚 Documentation

- **API Reference**: Visit `/docs` when server is running
- **Deployment Guide**: See `FASTAPI_DEPLOYMENT_GUIDE.md`
- **Integration Examples**: Check `examples/` folder

## ⚠️ Deployment Considerations

### Vercel Limitations
- **Timeout**: 10-30 second limits (AI processing may exceed this)
- **Cold Starts**: May affect response times
- **Memory**: Limited memory for AI operations

### Recommended Alternatives
1. **Railway**: Best performance, easy deployment
2. **Render**: Good free tier, reliable
3. **Google Cloud Run**: Serverless with better limits
4. **Self-hosted**: Maximum control and performance

## 🔒 Security

- Never commit API keys to Git
- Use environment variables for all secrets
- Set appropriate CORS origins in production
- Monitor API usage and implement rate limiting

## 🆘 Support

- **Issues**: Create GitHub issues for bugs or questions
- **Documentation**: Check `/docs` endpoint for API reference
- **Examples**: See `examples/` folder for integration code

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for Maharashtra's urban development community**
