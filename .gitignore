# UDCPR RAG API - Git Ignore File

# Environment Variables and Secrets
.env
.env.local
.env.production
.env.staging
*.env
api.env
secrets.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.conda/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebooks
.ipynb_checkpoints

# Logs
*.log
logs/
log/

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Vercel
.vercel

# API keys and credentials (extra safety)
*api_key*
*secret*
*password*
*token*
credentials.json
service-account.json

# Local testing
test_output/
debug/
