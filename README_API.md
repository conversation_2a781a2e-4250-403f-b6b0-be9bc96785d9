# UDCPR RAG Chatbot - FastAPI Server

🎉 **Your Streamlit app has been successfully converted to a standalone FastAPI server!**

This is a much better solution than trying to deploy Streamlit on Vercel. You now have a professional REST API that can be integrated with any frontend, mobile app, or other service.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r api_requirements.txt
```

### 2. Set Environment Variables
```bash
cp api.env.example .env
# Edit .env with your API keys
```

### 3. Start the Server
```bash
python start_api.py
```

### 4. Test the API
- **Interactive Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health
- **Example Frontend**: Open `examples/html_example.html`

## 📁 What's New

### Core API Files
- `api_server.py` - Main FastAPI application
- `api/models.py` - Request/response models
- `api/config.py` - Configuration management
- `api/services/rag_service.py` - Core RAG functionality

### Integration Examples
- `examples/javascript_integration.js` - JavaScript SDK
- `examples/react_component.jsx` - React component
- `examples/html_example.html` - Complete HTML example

### Deployment Configs
- `railway.toml` - Railway deployment (recommended)
- `render_api.yaml` - Render deployment
- `Dockerfile` - Docker deployment
- `docker-compose.yml` - Local Docker development

## 🌐 API Endpoints

### POST /api/chat
Send messages to the chatbot
```bash
curl -X POST "http://localhost:8000/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What are setback requirements?"}'
```

### POST /api/search
Search documents directly
```bash
curl -X POST "http://localhost:8000/api/search" \
  -H "Content-Type: application/json" \
  -d '{"query": "building height", "top_k": 5}'
```

### GET /api/health
Check API health
```bash
curl "http://localhost:8000/api/health"
```

## 🔗 Frontend Integration

### JavaScript
```javascript
const chatbot = new UDCPRChatbot('http://localhost:8000');
const response = await chatbot.sendMessage('What is FSI?');
```

### React
```jsx
import UDCPRChat from './react_component.jsx';
<UDCPRChat apiBaseUrl="http://localhost:8000" />
```

### Any Framework
The API works with any frontend framework or programming language that can make HTTP requests.

## 🚀 Deployment

### Recommended: Railway
1. Push to GitHub
2. Connect to Railway
3. Set environment variables
4. Deploy!

### Also Great: Render
1. Push to GitHub
2. Connect to Render
3. Use `render_api.yaml` config
4. Deploy!

### See Full Guide
Check `FASTAPI_DEPLOYMENT_GUIDE.md` for complete deployment instructions.

## 📚 Documentation

- `API_DOCUMENTATION.md` - Complete API reference
- `FASTAPI_DEPLOYMENT_GUIDE.md` - Deployment guide
- Interactive docs at `/docs` when server is running

## ✅ Advantages Over Streamlit + Vercel

1. **No Timeout Issues** - Proper server architecture
2. **Better Performance** - Optimized for API usage
3. **Universal Integration** - Works with any frontend
4. **Professional Architecture** - Industry-standard REST API
5. **Easier Deployment** - Multiple platform options
6. **Better Monitoring** - Health checks and logging
7. **Scalable** - Can handle concurrent requests properly

## 🔧 Environment Variables

Required:
```bash
OPENAI_API_KEY=your_openai_api_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX_NAME=udcpr-rag-index
```

Optional:
```bash
ENABLE_WEB_SEARCH=false
SUPABASE_URL=your_supabase_url
SUPABASE_API_KEY=your_supabase_key
```

## 🆘 Need Help?

1. **Check the docs**: Visit `/docs` when server is running
2. **Review logs**: Server provides detailed error messages
3. **Test endpoints**: Use the interactive documentation
4. **Check examples**: See `examples/` folder for integration code

## 🎉 Success!

You now have a professional, scalable API server that can be deployed anywhere and integrated with any frontend. This is a much better architecture than trying to force Streamlit into serverless platforms.

**Next Steps:**
1. Test the API locally
2. Try the HTML example
3. Deploy to Railway or Render
4. Integrate with your website/app
