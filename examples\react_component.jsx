/**
 * React Component Example for UDCPR RAG API Integration
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { UDCPRChatbot } from './javascript_integration.js';

// Custom hook for UDCPR Chatbot
function useUDCPRChatbot(apiBaseUrl) {
    const [chatbot] = useState(() => new UDCPRChatbot(apiBaseUrl));
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    const sendMessage = useCallback(async (message, options = {}) => {
        setIsLoading(true);
        setError(null);
        
        // Add user message immediately
        const userMessage = { 
            role: 'user', 
            content: message, 
            timestamp: new Date(),
            id: Date.now() + Math.random()
        };
        setMessages(prev => [...prev, userMessage]);

        try {
            const response = await chatbot.sendMessage(message, options);
            
            // Add assistant response
            const assistantMessage = {
                role: 'assistant', 
                content: response.response, 
                timestamp: new Date(),
                id: Date.now() + Math.random() + 1,
                sources: response.sources,
                webSearchUsed: response.web_search_used,
                processingTime: response.processing_time,
                tokenUsage: response.token_usage
            };
            setMessages(prev => [...prev, assistantMessage]);

            return response;
        } catch (error) {
            setError(error.message);
            // Add error message
            const errorMessage = {
                role: 'assistant', 
                content: `I apologize, but I encountered an error: ${error.message}`, 
                timestamp: new Date(),
                id: Date.now() + Math.random() + 2,
                isError: true
            };
            setMessages(prev => [...prev, errorMessage]);
            throw error;
        } finally {
            setIsLoading(false);
        }
    }, [chatbot]);

    const clearHistory = useCallback(() => {
        chatbot.clearHistory();
        setMessages([]);
        setError(null);
    }, [chatbot]);

    return {
        messages,
        sendMessage,
        clearHistory,
        isLoading,
        error,
        chatbot
    };
}

// Message Component
function Message({ message, onSourceClick }) {
    const isUser = message.role === 'user';
    
    return (
        <div className={`message ${isUser ? 'user' : 'assistant'} ${message.isError ? 'error' : ''}`}>
            <div className="message-header">
                <div className="message-avatar">
                    {isUser ? '👤' : '🤖'}
                </div>
                <div className="message-info">
                    <span className="message-role">
                        {isUser ? 'You' : 'UDCPR Assistant'}
                    </span>
                    <span className="message-time">
                        {message.timestamp.toLocaleTimeString()}
                    </span>
                </div>
            </div>
            
            <div className="message-content">
                {message.content}
            </div>
            
            {message.sources && message.sources.length > 0 && (
                <div className="message-sources">
                    <details>
                        <summary>
                            📚 Sources ({message.sources.length})
                        </summary>
                        <div className="sources-list">
                            {message.sources.map((source, index) => (
                                <div 
                                    key={index} 
                                    className="source-item"
                                    onClick={() => onSourceClick?.(source)}
                                >
                                    <div className="source-score">
                                        Score: {(source.score * 100).toFixed(1)}%
                                    </div>
                                    <div className="source-info">
                                        <strong>{source.metadata.source || 'Unknown Document'}</strong>
                                        {source.metadata.page_num && (
                                            <span> - Page {source.metadata.page_num}</span>
                                        )}
                                    </div>
                                    <div className="source-preview">
                                        {source.text.substring(0, 150)}...
                                    </div>
                                </div>
                            ))}
                        </div>
                    </details>
                </div>
            )}
            
            {message.webSearchUsed && (
                <div className="web-search-indicator">
                    🌐 Web search was used for this response
                </div>
            )}
            
            {message.processingTime && (
                <div className="processing-time">
                    ⏱️ Processed in {message.processingTime.toFixed(2)}s
                </div>
            )}
        </div>
    );
}

// Main Chat Component
function UDCPRChat({ apiBaseUrl = 'http://localhost:8000' }) {
    const { messages, sendMessage, clearHistory, isLoading, error } = useUDCPRChatbot(apiBaseUrl);
    const [inputValue, setInputValue] = useState('');
    const [useWebSearch, setUseWebSearch] = useState(false);
    const messagesEndRef = useRef(null);
    const inputRef = useRef(null);

    // Auto-scroll to bottom
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    // Focus input on mount
    useEffect(() => {
        inputRef.current?.focus();
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        const message = inputValue.trim();
        if (!message || isLoading) return;

        setInputValue('');
        
        try {
            await sendMessage(message, { useWebSearch });
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    };

    const handleSourceClick = (source) => {
        // Handle source click - could open a modal, navigate to document, etc.
        console.log('Source clicked:', source);
        alert(`Source: ${source.metadata.source}\nPage: ${source.metadata.page_num}\nRelevance: ${(source.score * 100).toFixed(1)}%`);
    };

    return (
        <div className="udcpr-chat">
            <div className="chat-header">
                <h2>📚 UDCPR Document Assistant</h2>
                <p>Ask questions about Maharashtra's Unified Development Control and Promotion Regulations</p>
                {error && (
                    <div className="error-banner">
                        ⚠️ {error}
                    </div>
                )}
            </div>
            
            <div className="chat-messages">
                {messages.length === 0 && (
                    <div className="welcome-message">
                        <div className="welcome-content">
                            <h3>Welcome! 👋</h3>
                            <p>I'm here to help you with questions about UDCPR regulations. You can ask about:</p>
                            <ul>
                                <li>Building height restrictions</li>
                                <li>Setback requirements</li>
                                <li>Floor Space Index (FSI) calculations</li>
                                <li>Zoning regulations</li>
                                <li>Development permissions</li>
                                <li>And much more!</li>
                            </ul>
                        </div>
                    </div>
                )}
                
                {messages.map((message) => (
                    <Message 
                        key={message.id} 
                        message={message} 
                        onSourceClick={handleSourceClick}
                    />
                ))}
                
                {isLoading && (
                    <div className="message assistant loading">
                        <div className="message-header">
                            <div className="message-avatar">🤖</div>
                            <div className="message-info">
                                <span className="message-role">UDCPR Assistant</span>
                            </div>
                        </div>
                        <div className="message-content">
                            <div className="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            Searching documents and generating response...
                        </div>
                    </div>
                )}
                
                <div ref={messagesEndRef} />
            </div>
            
            <div className="chat-input-container">
                <div className="chat-options">
                    <label className="web-search-toggle">
                        <input
                            type="checkbox"
                            checked={useWebSearch}
                            onChange={(e) => setUseWebSearch(e.target.checked)}
                        />
                        🌐 Enable web search
                    </label>
                    
                    <button 
                        type="button" 
                        onClick={clearHistory}
                        className="clear-button"
                        disabled={isLoading}
                    >
                        🗑️ Clear Chat
                    </button>
                </div>
                
                <form onSubmit={handleSubmit} className="chat-form">
                    <input
                        ref={inputRef}
                        type="text"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder="Ask a question about UDCPR regulations..."
                        disabled={isLoading}
                        className="chat-input"
                        maxLength={2000}
                    />
                    <button 
                        type="submit" 
                        disabled={isLoading || !inputValue.trim()}
                        className="send-button"
                    >
                        {isLoading ? '⏳' : '📤'} Send
                    </button>
                </form>
            </div>
        </div>
    );
}

export default UDCPRChat;
