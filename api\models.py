"""
Pydantic models for API request/response validation
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class ChatMessage(BaseModel):
    """Individual chat message"""
    role: str = Field(..., description="Message role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: Optional[datetime] = Field(default=None, description="Message timestamp")

class ChatRequest(BaseModel):
    """Request model for chat endpoint"""
    message: str = Field(..., min_length=1, max_length=2000, description="User message")
    chat_history: Optional[List[ChatMessage]] = Field(default=None, description="Previous chat history")
    use_web_search: Optional[bool] = Field(default=False, description="Enable web search for additional context")
    session_id: Optional[str] = Field(default=None, description="Session ID for conversation tracking")
    max_tokens: Optional[int] = Field(default=800, ge=100, le=2000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(default=0.5, ge=0.0, le=2.0, description="Response creativity (0-2)")

    class Config:
        json_schema_extra = {
            "example": {
                "message": "What are the setback requirements for residential buildings?",
                "chat_history": [
                    {
                        "role": "user",
                        "content": "Hello"
                    },
                    {
                        "role": "assistant", 
                        "content": "Hello! I'm here to help with UDCPR regulations."
                    }
                ],
                "use_web_search": False,
                "session_id": "session_123",
                "max_tokens": 800,
                "temperature": 0.5
            }
        }

class SearchResult(BaseModel):
    """Individual search result"""
    id: str = Field(..., description="Document chunk ID")
    score: float = Field(..., description="Relevance score (0-1)")
    text: str = Field(..., description="Document text content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")

class ChatResponse(BaseModel):
    """Response model for chat endpoint"""
    response: str = Field(..., description="Assistant response")
    sources: List[SearchResult] = Field(default_factory=list, description="Source documents used")
    session_id: Optional[str] = Field(default=None, description="Session ID")
    web_search_used: bool = Field(default=False, description="Whether web search was used")
    web_search_results: Optional[List[Dict[str, Any]]] = Field(default=None, description="Web search results if used")
    processing_time: float = Field(..., description="Processing time in seconds")
    token_usage: Optional[Dict[str, int]] = Field(default=None, description="Token usage statistics")

    class Config:
        json_schema_extra = {
            "example": {
                "response": "According to UDCPR regulations, residential buildings must maintain specific setback requirements...",
                "sources": [
                    {
                        "id": "chunk_123",
                        "score": 0.85,
                        "text": "Setback requirements for residential buildings...",
                        "metadata": {
                            "source": "UDCPR_document.pdf",
                            "page": 45,
                            "section": "Residential Regulations"
                        }
                    }
                ],
                "session_id": "session_123",
                "web_search_used": False,
                "processing_time": 2.3,
                "token_usage": {
                    "prompt_tokens": 150,
                    "completion_tokens": 200,
                    "total_tokens": 350
                }
            }
        }

class SearchRequest(BaseModel):
    """Request model for search endpoint"""
    query: str = Field(..., min_length=1, max_length=500, description="Search query")
    top_k: Optional[int] = Field(default=5, ge=1, le=20, description="Number of results to return")
    include_metadata: Optional[bool] = Field(default=True, description="Include document metadata")
    min_score: Optional[float] = Field(default=0.0, ge=0.0, le=1.0, description="Minimum relevance score")

    class Config:
        json_schema_extra = {
            "example": {
                "query": "building height restrictions",
                "top_k": 5,
                "include_metadata": True,
                "min_score": 0.5
            }
        }

class SearchResponse(BaseModel):
    """Response model for search endpoint"""
    results: List[SearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results found")
    query: str = Field(..., description="Original search query")
    processing_time: float = Field(..., description="Processing time in seconds")

    class Config:
        json_schema_extra = {
            "example": {
                "results": [
                    {
                        "id": "chunk_456",
                        "score": 0.92,
                        "text": "Building height restrictions in residential zones...",
                        "metadata": {
                            "source": "UDCPR_document.pdf",
                            "page": 67,
                            "section": "Height Regulations"
                        }
                    }
                ],
                "total_results": 1,
                "query": "building height restrictions",
                "processing_time": 0.8
            }
        }

class HealthResponse(BaseModel):
    """Response model for health check endpoint"""
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    services: Dict[str, str] = Field(..., description="Status of individual services")
    version: str = Field(..., description="API version")
    uptime: float = Field(..., description="Service uptime in seconds")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2024-01-15T10:30:00Z",
                "services": {
                    "openai": "connected",
                    "pinecone": "connected",
                    "web_search": "available"
                },
                "version": "1.0.0",
                "uptime": 3600.5
            }
        }

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid request parameters",
                "details": {
                    "field": "message",
                    "issue": "Field required"
                },
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }
