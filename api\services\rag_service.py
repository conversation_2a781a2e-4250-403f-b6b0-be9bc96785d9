"""
RAG Service - Core functionality for the UDCPR RAG Chatbot
Integrates OpenAI, Pinecone, and web search capabilities
"""

import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

import openai
import pinecone
from tenacity import retry, stop_after_attempt, wait_exponential

from api.config import Settings
from api.models import ChatMessage, SearchResult
from api.services.legacy_integration import legacy_integration

logger = logging.getLogger(__name__)

class RAGService:
    """Main RAG service class"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.openai_client = None
        self.pinecone_client = None
        self.pinecone_index = None
        self.start_time = time.time()
        
    async def initialize(self):
        """Initialize all services"""
        logger.info("Initializing RAG Service...")
        
        # Initialize OpenAI
        self.openai_client = openai.OpenAI(api_key=self.settings.openai_api_key)
        logger.info("OpenAI client initialized")
        
        # Initialize Pinecone
        self.pinecone_client = pinecone.Pinecone(api_key=self.settings.pinecone_api_key)
        
        # Check if index exists
        index_list = [index.name for index in self.pinecone_client.list_indexes()]
        if self.settings.pinecone_index_name not in index_list:
            raise ValueError(f"Pinecone index '{self.settings.pinecone_index_name}' does not exist")
        
        self.pinecone_index = self.pinecone_client.Index(self.settings.pinecone_index_name)
        logger.info(f"Pinecone index '{self.settings.pinecone_index_name}' connected")
        
        logger.info("RAG Service initialization complete")
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up RAG Service...")
        # Add any cleanup logic here if needed
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all services"""
        services = {}
        
        # Check OpenAI
        try:
            # Simple API call to test connection
            response = self.openai_client.models.list()
            services["openai"] = "connected"
        except Exception as e:
            logger.error(f"OpenAI health check failed: {e}")
            services["openai"] = f"error: {str(e)}"
        
        # Check Pinecone
        try:
            stats = self.pinecone_index.describe_index_stats()
            services["pinecone"] = "connected"
        except Exception as e:
            logger.error(f"Pinecone health check failed: {e}")
            services["pinecone"] = f"error: {str(e)}"
        
        # Check web search availability
        if self.settings.enable_web_search:
            try:
                # Import here to avoid dependency issues if not available
                from web_search import perform_web_search
                services["web_search"] = "available"
            except ImportError:
                services["web_search"] = "not_available"
        else:
            services["web_search"] = "disabled"
        
        overall_status = "healthy" if all(
            status in ["connected", "available", "disabled"] 
            for status in services.values()
        ) else "unhealthy"
        
        return {
            "status": overall_status,
            "timestamp": datetime.now(),
            "services": services,
            "version": self.settings.app_version,
            "uptime": time.time() - self.start_time
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text"""
        try:
            response = self.openai_client.embeddings.create(
                input=[text],
                model=self.settings.openai_embedding_model,
                dimensions=self.settings.openai_embedding_dimensions
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            raise
    
    async def search_documents(
        self,
        query: str,
        top_k: int = None,
        include_metadata: bool = True,
        min_score: float = None
    ) -> Dict[str, Any]:
        """Search documents in Pinecone"""
        start_time = time.time()

        if top_k is None:
            top_k = self.settings.top_k_results
        if min_score is None:
            min_score = self.settings.min_relevance_score

        try:
            # Try to use legacy search first if available
            if legacy_integration.search_pinecone:
                logger.info("Using legacy search interface")
                raw_results = legacy_integration.search_documents_legacy(query, top_k)

                # Convert to our format
                results = []
                for match in raw_results:
                    score = match.get("score", 0)
                    if score >= min_score:
                        result = SearchResult(
                            id=match.get("id", ""),
                            score=score,
                            text=match.get("metadata", {}).get("text", ""),
                            metadata=match.get("metadata", {})
                        )
                        results.append(result)
            else:
                # Fallback to direct Pinecone search
                logger.info("Using direct Pinecone search")
                query_embedding = await self.get_embedding(query)

                search_response = self.pinecone_index.query(
                    vector=query_embedding,
                    top_k=top_k,
                    include_metadata=include_metadata
                )

                results = []
                for match in search_response.get("matches", []):
                    score = match.get("score", 0)
                    if score >= min_score:
                        result = SearchResult(
                            id=match.get("id", ""),
                            score=score,
                            text=match.get("metadata", {}).get("text", ""),
                            metadata=match.get("metadata", {})
                        )
                        results.append(result)

            processing_time = time.time() - start_time

            return {
                "results": results,
                "total_results": len(results),
                "query": query,
                "processing_time": processing_time
            }

        except Exception as e:
            logger.error(f"Document search failed: {e}")
            raise
    
    def format_context_from_results(self, results: List[SearchResult]) -> str:
        """Format search results into context string"""
        if not results:
            return "No relevant information found in the UDCPR document."
        
        context_parts = []
        for result in results:
            if result.text and result.score > self.settings.min_relevance_score:
                source = result.metadata.get("source", "Unknown")
                page = result.metadata.get("page_num", "Unknown")
                context_parts.append(f"[Source: {source}, Page: {page}]\n{result.text}\n")
        
        if not context_parts:
            return "No sufficiently relevant information found in the UDCPR document."
        
        return "\n".join(context_parts)
    
    def create_chat_prompt(
        self, 
        query: str, 
        context: str, 
        web_search_context: Optional[str] = None,
        chat_history: List[ChatMessage] = None
    ) -> List[Dict[str, str]]:
        """Create chat prompt for OpenAI"""
        messages = []
        
        # System message
        system_message = """You are an expert assistant for the Unified Development Control and Promotion Regulations (UDCPR) for Maharashtra State, India. 
Your task is to provide accurate, helpful information about building regulations, zoning requirements, and development control rules based on the UDCPR document.

When answering:
1. Base your answers primarily on the provided context from the UDCPR document
2. If the context contains the information, provide detailed, accurate answers
3. If the context doesn't contain enough information, acknowledge the limitations
4. Be concise but thorough
5. Use bullet points or numbered lists for clarity when appropriate
6. If asked about something outside the scope of UDCPR, politely explain that you're focused on UDCPR regulations
7. If web search results are provided, you may use them to supplement your answer, but clearly indicate when information comes from external sources

Remember, your goal is to help users understand and navigate the UDCPR regulations accurately."""
        
        messages.append({"role": "system", "content": system_message})
        
        # Add chat history
        if chat_history:
            recent_history = chat_history[-self.settings.max_history_messages:]
            for msg in recent_history:
                messages.append({"role": msg.role, "content": msg.content})
        
        # Construct user message with context
        user_message = f"Question: {query}\n\nRelevant sections from the UDCPR document:\n{context}"
        
        if web_search_context:
            user_message += f"\n\nAdditional information from web search:\n{web_search_context}"
        
        messages.append({"role": "user", "content": user_message})
        
        return messages

    async def perform_web_search(self, query: str) -> Optional[str]:
        """Perform web search if enabled"""
        if not self.settings.enable_web_search:
            return None

        try:
            # Import here to avoid dependency issues
            from web_search import perform_web_search, format_search_results_for_context

            web_results = perform_web_search(query, num_results=self.settings.web_search_num_results)
            if web_results:
                return format_search_results_for_context(web_results)
            return None

        except ImportError:
            logger.warning("Web search module not available")
            return None
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return None

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_response(
        self,
        query: str,
        chat_history: List[ChatMessage] = None,
        use_web_search: bool = False,
        session_id: Optional[str] = None,
        max_tokens: int = 800,
        temperature: float = 0.5
    ) -> Dict[str, Any]:
        """Generate response using RAG"""
        start_time = time.time()

        try:
            # Search for relevant documents
            search_results = await self.search_documents(query, top_k=self.settings.top_k_results)
            context = self.format_context_from_results(search_results["results"])

            # Perform web search if requested
            web_search_context = None
            web_search_results = None
            if use_web_search:
                web_search_context = await self.perform_web_search(query)
                if web_search_context:
                    # Store web search results for response
                    web_search_results = [{"context": web_search_context}]

            # Create chat prompt
            messages = self.create_chat_prompt(query, context, web_search_context, chat_history)

            # Generate response
            response = self.openai_client.chat.completions.create(
                model=self.settings.openai_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            processing_time = time.time() - start_time

            return {
                "response": response.choices[0].message.content,
                "sources": search_results["results"],
                "session_id": session_id,
                "web_search_used": bool(web_search_context),
                "web_search_results": web_search_results,
                "processing_time": processing_time,
                "token_usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                } if response.usage else None
            }

        except Exception as e:
            logger.error(f"Response generation failed: {e}")
            raise
