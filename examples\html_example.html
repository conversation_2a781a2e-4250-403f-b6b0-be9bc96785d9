<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UDCPR RAG Chatbot - HTML Example</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: 90vh;
            margin-top: 5vh;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }

        .chat-header h1 {
            color: #333;
            margin-bottom: 5px;
        }

        .chat-header p {
            color: #666;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user {
            background-color: #007bff;
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }

        .message.assistant {
            background-color: #f1f1f1;
            color: #333;
            align-self: flex-start;
        }

        .message.loading {
            background-color: #e9ecef;
            color: #666;
            font-style: italic;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .sources {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 12px;
        }

        .sources details {
            cursor: pointer;
        }

        .sources summary {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .sources ul {
            list-style: none;
            padding-left: 0;
        }

        .sources li {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid #eee;
        }

        .chat-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .web-search-toggle {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #666;
        }

        .clear-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .clear-button:hover {
            background: #c82333;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }

        .chat-input:focus {
            border-color: #007bff;
        }

        .send-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .welcome-message h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .welcome-message ul {
            text-align: left;
            max-width: 400px;
            margin: 0 auto;
        }

        .welcome-message li {
            margin: 5px 0;
        }

        .typing-indicator {
            display: inline-flex;
            gap: 3px;
            margin-right: 8px;
        }

        .typing-indicator span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #666;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .error-banner {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>📚 UDCPR Document Assistant</h1>
            <p>Ask questions about Maharashtra's Unified Development Control and Promotion Regulations</p>
            <div id="error-banner" class="error-banner" style="display: none;"></div>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <div class="welcome-message">
                <h3>Welcome! 👋</h3>
                <p>I'm here to help you with questions about UDCPR regulations. You can ask about:</p>
                <ul>
                    <li>Building height restrictions</li>
                    <li>Setback requirements</li>
                    <li>Floor Space Index (FSI) calculations</li>
                    <li>Zoning regulations</li>
                    <li>Development permissions</li>
                    <li>And much more!</li>
                </ul>
            </div>
        </div>
        
        <div class="chat-input-area">
            <div class="chat-options">
                <label class="web-search-toggle">
                    <input type="checkbox" id="web-search-toggle">
                    🌐 Enable web search
                </label>
                <button class="clear-button" id="clear-button">🗑️ Clear Chat</button>
            </div>
            
            <div class="input-container">
                <input 
                    type="text" 
                    id="chat-input" 
                    class="chat-input" 
                    placeholder="Ask a question about UDCPR regulations..."
                    maxlength="2000"
                >
                <button id="send-button" class="send-button">📤 Send</button>
            </div>
        </div>
    </div>

    <script src="javascript_integration.js"></script>
    <script>
        // Initialize the chatbot
        const API_BASE_URL = 'http://localhost:8000'; // Change this to your API URL
        const chatbot = new UDCPRChatbot(API_BASE_URL);
        
        // DOM elements
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const clearButton = document.getElementById('clear-button');
        const webSearchToggle = document.getElementById('web-search-toggle');
        const errorBanner = document.getElementById('error-banner');

        let isLoading = false;

        function showError(message) {
            errorBanner.textContent = `⚠️ ${message}`;
            errorBanner.style.display = 'block';
            setTimeout(() => {
                errorBanner.style.display = 'none';
            }, 5000);
        }

        function addMessage(role, content, options = {}) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role} ${options.isLoading ? 'loading' : ''} ${options.isError ? 'error' : ''}`;
            messageDiv.innerHTML = content;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageDiv;
        }

        function addSources(sources) {
            if (!sources || sources.length === 0) return;
            
            const sourcesDiv = document.createElement('div');
            sourcesDiv.className = 'sources';
            sourcesDiv.innerHTML = `
                <details>
                    <summary>📚 Sources (${sources.length})</summary>
                    <ul>
                        ${sources.map(source => `
                            <li>
                                <strong>Score:</strong> ${(source.score * 100).toFixed(1)}% | 
                                <strong>Source:</strong> ${source.metadata.source || 'Unknown'} | 
                                <strong>Page:</strong> ${source.metadata.page_num || 'Unknown'}
                                <br><small>${source.text.substring(0, 150)}...</small>
                            </li>
                        `).join('')}
                    </ul>
                </details>
            `;
            chatMessages.appendChild(sourcesDiv);
        }

        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isLoading) return;

            isLoading = true;
            sendButton.disabled = true;
            sendButton.textContent = '⏳ Sending...';

            // Clear welcome message if it exists
            const welcomeMessage = chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            // Add user message
            addMessage('user', message);
            chatInput.value = '';

            // Add loading message
            const loadingMessage = addMessage('assistant', `
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                Searching documents and generating response...
            `, { isLoading: true });

            try {
                const response = await chatbot.sendMessage(message, {
                    useWebSearch: webSearchToggle.checked
                });

                // Remove loading message
                loadingMessage.remove();

                // Add assistant response
                let responseContent = response.response;
                if (response.web_search_used) {
                    responseContent += '<br><small>🌐 Web search was used for this response</small>';
                }
                if (response.processing_time) {
                    responseContent += `<br><small>⏱️ Processed in ${response.processing_time.toFixed(2)}s</small>`;
                }

                addMessage('assistant', responseContent);
                addSources(response.sources);

            } catch (error) {
                loadingMessage.remove();
                addMessage('assistant', `I apologize, but I encountered an error: ${error.message}`, { isError: true });
                showError(error.message);
            } finally {
                isLoading = false;
                sendButton.disabled = false;
                sendButton.textContent = '📤 Send';
                chatInput.focus();
            }
        }

        function clearChat() {
            chatbot.clearHistory();
            chatMessages.innerHTML = `
                <div class="welcome-message">
                    <h3>Welcome! 👋</h3>
                    <p>I'm here to help you with questions about UDCPR regulations. You can ask about:</p>
                    <ul>
                        <li>Building height restrictions</li>
                        <li>Setback requirements</li>
                        <li>Floor Space Index (FSI) calculations</li>
                        <li>Zoning regulations</li>
                        <li>Development permissions</li>
                        <li>And much more!</li>
                    </ul>
                </div>
            `;
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        clearButton.addEventListener('click', clearChat);
        
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Focus input on load
        chatInput.focus();

        // Test API connection on load
        chatbot.checkHealth().then(health => {
            console.log('API Health:', health);
        }).catch(error => {
            showError(`Failed to connect to API: ${error.message}`);
        });
    </script>
</body>
</html>
