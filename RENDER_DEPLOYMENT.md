# Render Deployment Guide (Great Alternative)

🌟 **Render is an excellent alternative to Vercel for your UDCPR RAG API**

Render provides a great balance of features, performance, and cost-effectiveness, with a generous free tier and excellent support for Python applications.

## ✅ Why Render is Great for Your API

- **Free Tier Available**: Perfect for testing and small projects
- **No Cold Starts on Paid Plans**: Better performance than serverless
- **30-minute Timeout**: Much better than Vercel's 30 seconds
- **Easy Deployment**: Simple GitHub integration
- **Persistent Storage**: Optional database and file storage
- **Custom Domains**: Free SSL certificates

## 🚀 Quick Deployment (10 Minutes)

### Step 1: Push to GitHub
```bash
git remote add origin https://github.com/clumsypasta/API-UDCPR.git
git branch -M main
git push -u origin main
```

### Step 2: Deploy to Render

1. **Go to Render**
   - Visit [render.com](https://render.com)
   - Sign up/Sign in with GitHub

2. **Create New Web Service**
   - Click "New +" → "Web Service"
   - Connect your GitHub account
   - Select `clumsypasta/API-UDCPR` repository

3. **Configure Service**
   - **Name**: `udcpr-rag-api`
   - **Environment**: `Python 3`
   - **Build Command**: `pip install -r api_requirements.txt`
   - **Start Command**: `uvicorn api_server:app --host 0.0.0.0 --port $PORT`

### Step 3: Set Environment Variables

In Render dashboard, go to **Environment** tab and add:

```bash
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index
PYTHONPATH=.
ENVIRONMENT=production
```

Optional variables:
```bash
OPENAI_MODEL=gpt-4o
ENABLE_WEB_SEARCH=false
MAX_HISTORY_MESSAGES=10
TOP_K_RESULTS=5
LOG_LEVEL=INFO
```

### Step 4: Deploy!

1. Click **"Create Web Service"**
2. Render builds and deploys automatically
3. Get your URL: `https://udcpr-rag-api.onrender.com`

## 🧪 Test Your Deployment

```bash
# Health check
curl https://udcpr-rag-api.onrender.com/api/health

# Chat endpoint
curl -X POST "https://udcpr-rag-api.onrender.com/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What are setback requirements?"}'

# Interactive docs
# Visit: https://udcpr-rag-api.onrender.com/docs
```

## 📊 Expected Performance

### Free Tier
- **Health Check**: 2-5 seconds (with cold starts)
- **Chat Requests**: 15-30 seconds (including cold start)
- **Cold Starts**: 30-60 seconds after inactivity

### Paid Tier ($7/month)
- **Health Check**: < 1 second
- **Chat Requests**: 3-8 seconds
- **Cold Starts**: None (persistent containers)

## 🔧 Render Configuration Options

### Using render_api.yaml (Recommended)

Your project includes `render_api.yaml` for automatic configuration:

```yaml
services:
  - type: web
    name: udcpr-rag-api
    env: python
    buildCommand: pip install -r api_requirements.txt
    startCommand: uvicorn api_server:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /api/health
    healthCheckTimeout: 60
```

### Manual Configuration

If not using the YAML file:
- **Runtime**: Python 3.10
- **Build Command**: `pip install -r api_requirements.txt`
- **Start Command**: `uvicorn api_server:app --host 0.0.0.0 --port $PORT`
- **Health Check Path**: `/api/health`

## 💰 Pricing

### Free Tier
- **750 hours/month** (enough for testing)
- **512MB RAM**
- **0.1 CPU**
- **Cold starts** after 15 minutes of inactivity

### Starter Plan ($7/month)
- **Always on** (no cold starts)
- **512MB RAM**
- **0.5 CPU**
- **Custom domains**

### Standard Plan ($25/month)
- **2GB RAM**
- **1 CPU**
- **Better performance**

## 📈 Monitoring & Logs

Render provides good monitoring:

1. **Logs**: Real-time application logs
2. **Metrics**: Basic CPU and memory usage
3. **Health Checks**: Automatic monitoring
4. **Alerts**: Email notifications for issues

## 🔄 Continuous Deployment

Render automatically redeploys when you push to GitHub:

```bash
# Make changes
git add .
git commit -m "Update API"
git push

# Render automatically redeploys!
```

## 🌐 Custom Domain

1. Go to **Settings** → **Custom Domains**
2. Add your domain
3. Update DNS records as instructed
4. SSL certificate is automatic and free

## 🛠️ Advanced Configuration

### Environment Variables for Production
```bash
# Performance optimization
WORKERS=2
WORKER_CLASS=uvicorn.workers.UvicornWorker
MAX_WORKERS=4

# Logging
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1

# API optimization
MAX_TOKENS=600
TEMPERATURE=0.5
TOP_K_RESULTS=3
```

### Health Check Configuration
```bash
# In render_api.yaml
healthCheckPath: /api/health
healthCheckTimeout: 60
```

## 🆘 Troubleshooting

### Build Fails
1. Check build logs in Render dashboard
2. Verify `api_requirements.txt` exists
3. Ensure all dependencies are compatible
4. Try using minimal requirements

### App Won't Start
1. Check start command is correct
2. Verify environment variables are set
3. Review application logs
4. Ensure port binding is correct

### Slow Performance on Free Tier
1. This is expected due to cold starts
2. Upgrade to Starter plan ($7/month) for better performance
3. Optimize your API for faster responses
4. Consider caching frequently requested data

### Memory Issues
1. Monitor memory usage in dashboard
2. Optimize your code to use less memory
3. Upgrade to Standard plan for more RAM
4. Reduce batch sizes in AI processing

## 🎯 Render vs Other Platforms

| Feature | Render | Railway | Vercel |
|---------|--------|---------|--------|
| **Free Tier** | ✅ 750 hours | ❌ None | ✅ Limited |
| **AI Workloads** | ✅ Good | ✅ Excellent | ❌ Poor |
| **Timeout** | ✅ 30 minutes | ✅ None | ❌ 30 seconds |
| **Cold Starts** | ⚠️ Free tier only | ✅ None | ❌ Always |
| **Setup** | ✅ Easy | ✅ Very easy | ⚠️ Complex |
| **Pricing** | ✅ $7/month | ✅ $5/month | ⚠️ Usage-based |

## 🎉 Success!

Once deployed on Render, your API will be:
- **Accessible**: Available at your Render URL
- **Documented**: Interactive docs at `/docs`
- **Monitored**: Health checks and logging
- **Scalable**: Easy to upgrade plans

**Your API endpoints:**
- Main: `https://udcpr-rag-api.onrender.com/`
- Health: `https://udcpr-rag-api.onrender.com/api/health`
- Chat: `https://udcpr-rag-api.onrender.com/api/chat`
- Docs: `https://udcpr-rag-api.onrender.com/docs`

## 🔗 Integration

Use your Render URL in frontend applications:

```javascript
const chatbot = new UDCPRChatbot('https://udcpr-rag-api.onrender.com');
const response = await chatbot.sendMessage('What is FSI calculation?');
```

## 💡 Pro Tips

1. **Start with Free Tier** to test everything works
2. **Upgrade to Starter** for production use (eliminates cold starts)
3. **Use Health Checks** to ensure your API stays responsive
4. **Monitor Logs** to debug any issues
5. **Set up Alerts** for downtime notifications

**Render is an excellent choice for your UDCPR RAG API!** 🌟
