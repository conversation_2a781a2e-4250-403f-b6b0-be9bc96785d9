"""
Legacy integration module to work with existing UDCPR RAG modules
This module provides compatibility with the existing codebase
"""

import sys
import os
import logging
from typing import List, Dict, Any, Optional

# Add the parent directory to the path to import existing modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

logger = logging.getLogger(__name__)

class LegacyRAGIntegration:
    """Integration layer for existing RAG functionality"""
    
    def __init__(self):
        self.query_interface = None
        self.web_search = None
        self.rag_chatbot = None
        self._initialize_modules()
    
    def _initialize_modules(self):
        """Initialize existing modules"""
        try:
            # Import existing query interface
            from query_interface import search_pinecone
            self.search_pinecone = search_pinecone
            logger.info("Query interface module loaded")
        except ImportError as e:
            logger.warning(f"Could not import query_interface: {e}")
            self.search_pinecone = None
        
        try:
            # Import existing RAG chatbot functionality
            from rag_chatbot import (
                format_context_from_results, 
                create_chat_prompt,
                MODEL, 
                MAX_HISTORY_MESSAGES, 
                TOP_K_RESULTS
            )
            self.format_context_from_results = format_context_from_results
            self.create_chat_prompt = create_chat_prompt
            self.MODEL = MODEL
            self.MAX_HISTORY_MESSAGES = MAX_HISTORY_MESSAGES
            self.TOP_K_RESULTS = TOP_K_RESULTS
            logger.info("RAG chatbot module loaded")
        except ImportError as e:
            logger.warning(f"Could not import rag_chatbot: {e}")
            self.format_context_from_results = None
            self.create_chat_prompt = None
        
        try:
            # Import web search functionality
            from web_search import perform_web_search, format_search_results_for_context
            self.perform_web_search = perform_web_search
            self.format_search_results_for_context = format_search_results_for_context
            logger.info("Web search module loaded")
        except ImportError as e:
            logger.warning(f"Could not import web_search: {e}")
            self.perform_web_search = None
            self.format_search_results_for_context = None
    
    def search_documents_legacy(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search documents using legacy query interface"""
        if not self.search_pinecone:
            raise RuntimeError("Legacy search functionality not available")
        
        try:
            results = self.search_pinecone(query, top_k=top_k, include_metadata=True)
            return results
        except Exception as e:
            logger.error(f"Legacy document search failed: {e}")
            raise
    
    def format_context_legacy(self, results: List[Dict[str, Any]]) -> str:
        """Format context using legacy function"""
        if not self.format_context_from_results:
            # Fallback formatting
            return self._fallback_format_context(results)
        
        try:
            return self.format_context_from_results(results)
        except Exception as e:
            logger.error(f"Legacy context formatting failed: {e}")
            return self._fallback_format_context(results)
    
    def _fallback_format_context(self, results: List[Dict[str, Any]]) -> str:
        """Fallback context formatting"""
        if not results:
            return "No relevant information found in the UDCPR document."
        
        context_parts = []
        for result in results:
            score = result.get("score", 0)
            text = result.get("metadata", {}).get("text", "")
            source = result.get("metadata", {}).get("source", "Unknown")
            page = result.get("metadata", {}).get("page_num", "Unknown")
            
            if text and score > 0.5:
                context_parts.append(f"[Source: {source}, Page: {page}]\n{text}\n")
        
        if not context_parts:
            return "No sufficiently relevant information found in the UDCPR document."
        
        return "\n".join(context_parts)
    
    def create_chat_prompt_legacy(
        self, 
        query: str, 
        context: str, 
        web_search_context: Optional[str] = None,
        chat_history: Optional[List[Dict[str, str]]] = None
    ) -> List[Dict[str, str]]:
        """Create chat prompt using legacy function"""
        if not self.create_chat_prompt:
            return self._fallback_create_chat_prompt(query, context, web_search_context, chat_history)
        
        try:
            return self.create_chat_prompt(query, context, web_search_context, chat_history)
        except Exception as e:
            logger.error(f"Legacy prompt creation failed: {e}")
            return self._fallback_create_chat_prompt(query, context, web_search_context, chat_history)
    
    def _fallback_create_chat_prompt(
        self, 
        query: str, 
        context: str, 
        web_search_context: Optional[str] = None,
        chat_history: Optional[List[Dict[str, str]]] = None
    ) -> List[Dict[str, str]]:
        """Fallback chat prompt creation"""
        messages = []
        
        # System message
        system_message = """You are an expert assistant for the Unified Development Control and Promotion Regulations (UDCPR) for Maharashtra State, India. 
Your task is to provide accurate, helpful information about building regulations, zoning requirements, and development control rules based on the UDCPR document.

When answering:
1. Base your answers primarily on the provided context from the UDCPR document
2. If the context contains the information, provide detailed, accurate answers
3. If the context doesn't contain enough information, acknowledge the limitations
4. Be concise but thorough
5. Use bullet points or numbered lists for clarity when appropriate
6. If asked about something outside the scope of UDCPR, politely explain that you're focused on UDCPR regulations
7. If web search results are provided, you may use them to supplement your answer, but clearly indicate when information comes from external sources

Remember, your goal is to help users understand and navigate the UDCPR regulations accurately."""
        
        messages.append({"role": "system", "content": system_message})
        
        # Add chat history
        if chat_history:
            max_history = getattr(self, 'MAX_HISTORY_MESSAGES', 10)
            recent_history = chat_history[-max_history:]
            messages.extend(recent_history)
        
        # Construct user message
        user_message = f"Question: {query}\n\nRelevant sections from the UDCPR document:\n{context}"
        
        if web_search_context:
            user_message += f"\n\nAdditional information from web search:\n{web_search_context}"
        
        messages.append({"role": "user", "content": user_message})
        
        return messages
    
    def perform_web_search_legacy(self, query: str, num_results: int = 3) -> Optional[str]:
        """Perform web search using legacy function"""
        if not self.perform_web_search or not self.format_search_results_for_context:
            logger.warning("Web search functionality not available")
            return None
        
        try:
            web_results = self.perform_web_search(query, num_results=num_results)
            if web_results:
                return self.format_search_results_for_context(web_results)
            return None
        except Exception as e:
            logger.error(f"Legacy web search failed: {e}")
            return None

# Global instance
legacy_integration = LegacyRAGIntegration()
