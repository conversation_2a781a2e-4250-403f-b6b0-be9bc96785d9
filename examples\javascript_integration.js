/**
 * UDCPR RAG API - JavaScript Integration Examples
 * 
 * This file contains examples of how to integrate with the UDCPR RAG API
 * from JavaScript/frontend applications.
 */

class UDCPRChatbot {
    constructor(apiBaseUrl, options = {}) {
        this.apiBaseUrl = apiBaseUrl.replace(/\/$/, ''); // Remove trailing slash
        this.options = {
            timeout: 30000, // 30 seconds
            retries: 3,
            ...options
        };
        this.sessionId = this.generateSessionId();
        this.chatHistory = [];
    }

    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    /**
     * Send a chat message to the API
     * @param {string} message - The user message
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} - API response
     */
    async sendMessage(message, options = {}) {
        const requestData = {
            message: message,
            chat_history: this.chatHistory,
            use_web_search: options.useWebSearch || false,
            session_id: this.sessionId,
            max_tokens: options.maxTokens || 800,
            temperature: options.temperature || 0.5
        };

        try {
            const response = await this.makeRequest('/api/chat', 'POST', requestData);
            
            // Update chat history
            this.chatHistory.push(
                { role: 'user', content: message, timestamp: new Date().toISOString() },
                { role: 'assistant', content: response.response, timestamp: new Date().toISOString() }
            );

            // Limit chat history to prevent it from growing too large
            if (this.chatHistory.length > 20) {
                this.chatHistory = this.chatHistory.slice(-20);
            }

            return response;
        } catch (error) {
            console.error('Chat request failed:', error);
            throw error;
        }
    }

    /**
     * Search documents directly
     * @param {string} query - Search query
     * @param {Object} options - Search options
     * @returns {Promise<Object>} - Search results
     */
    async searchDocuments(query, options = {}) {
        const requestData = {
            query: query,
            top_k: options.topK || 5,
            include_metadata: options.includeMetadata !== false,
            min_score: options.minScore || 0.0
        };

        try {
            return await this.makeRequest('/api/search', 'POST', requestData);
        } catch (error) {
            console.error('Search request failed:', error);
            throw error;
        }
    }

    /**
     * Check API health
     * @returns {Promise<Object>} - Health status
     */
    async checkHealth() {
        try {
            return await this.makeRequest('/api/health', 'GET');
        } catch (error) {
            console.error('Health check failed:', error);
            throw error;
        }
    }

    /**
     * Clear chat history
     */
    clearHistory() {
        this.chatHistory = [];
        this.sessionId = this.generateSessionId();
    }

    /**
     * Make HTTP request with retry logic
     * @private
     */
    async makeRequest(endpoint, method, data = null) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        
        for (let attempt = 1; attempt <= this.options.retries; attempt++) {
            try {
                const requestOptions = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    timeout: this.options.timeout
                };

                if (data && method !== 'GET') {
                    requestOptions.body = JSON.stringify(data);
                }

                const response = await fetch(url, requestOptions);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`HTTP ${response.status}: ${errorData.detail || response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                if (attempt === this.options.retries) {
                    throw error;
                }
                
                // Wait before retry (exponential backoff)
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
}

// Example usage with vanilla JavaScript
function createChatInterface() {
    const chatbot = new UDCPRChatbot('https://your-api-domain.com');
    
    const chatContainer = document.getElementById('chat-container');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');

    function addMessage(role, content, isLoading = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        messageDiv.innerHTML = `
            <div class="message-header">
                <strong>${role === 'user' ? 'You' : 'Assistant'}</strong>
                <span class="timestamp">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-content ${isLoading ? 'loading' : ''}">${content}</div>
        `;
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        return messageDiv;
    }

    async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // Add user message
        addMessage('user', message);
        messageInput.value = '';
        sendButton.disabled = true;

        // Add loading message
        const loadingMessage = addMessage('assistant', 'Thinking...', true);

        try {
            const response = await chatbot.sendMessage(message, {
                useWebSearch: document.getElementById('web-search-toggle')?.checked || false
            });

            // Remove loading message and add response
            loadingMessage.remove();
            addMessage('assistant', response.response);

            // Show sources if available
            if (response.sources && response.sources.length > 0) {
                const sourcesDiv = document.createElement('div');
                sourcesDiv.className = 'sources';
                sourcesDiv.innerHTML = `
                    <details>
                        <summary>Sources (${response.sources.length})</summary>
                        <ul>
                            ${response.sources.map(source => `
                                <li>
                                    <strong>Score:</strong> ${source.score.toFixed(2)} | 
                                    <strong>Source:</strong> ${source.metadata.source || 'Unknown'} | 
                                    <strong>Page:</strong> ${source.metadata.page_num || 'Unknown'}
                                </li>
                            `).join('')}
                        </ul>
                    </details>
                `;
                chatContainer.appendChild(sourcesDiv);
            }

        } catch (error) {
            loadingMessage.remove();
            addMessage('assistant', `Error: ${error.message}`);
        } finally {
            sendButton.disabled = false;
            messageInput.focus();
        }
    }

    // Event listeners
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    return chatbot;
}

// React Hook Example
function useUDCPRChatbot(apiBaseUrl) {
    const [chatbot] = useState(() => new UDCPRChatbot(apiBaseUrl));
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const sendMessage = useCallback(async (message, options = {}) => {
        setIsLoading(true);
        
        // Add user message immediately
        setMessages(prev => [...prev, { role: 'user', content: message, timestamp: new Date() }]);

        try {
            const response = await chatbot.sendMessage(message, options);
            
            // Add assistant response
            setMessages(prev => [...prev, { 
                role: 'assistant', 
                content: response.response, 
                timestamp: new Date(),
                sources: response.sources,
                webSearchUsed: response.web_search_used
            }]);

            return response;
        } catch (error) {
            // Add error message
            setMessages(prev => [...prev, { 
                role: 'assistant', 
                content: `Error: ${error.message}`, 
                timestamp: new Date(),
                isError: true
            }]);
            throw error;
        } finally {
            setIsLoading(false);
        }
    }, [chatbot]);

    const clearHistory = useCallback(() => {
        chatbot.clearHistory();
        setMessages([]);
    }, [chatbot]);

    return {
        messages,
        sendMessage,
        clearHistory,
        isLoading,
        chatbot
    };
}

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UDCPRChatbot };
}
