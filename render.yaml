services:
  - type: web
    name: udcpr-rag-chatbot
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: streamlit run chatbot_web.py --server.port=$PORT --server.address=0.0.0.0
    envVars:
      - key: PYTHONUNBUFFERED
        value: true
      - key: RENDER
        value: true
      - key: OPENAI_API_KEY
        sync: false
      - key: PINECONE_API_KEY
        sync: false
      - key: PINECONE_ENVIRONMENT
        value: us-east-1
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_API_KEY
        sync: false
      - key: ENABLE_WEB_SEARCH
        value: true
    healthCheckPath: /health
    healthCheckTimeout: 60
