{"version": 2, "name": "udcpr-rag-api", "builds": [{"src": "vercel_handler.py", "use": "@vercel/python", "config": {"maxLambdaSize": "50mb", "runtime": "python3.9"}}], "routes": [{"src": "/api/(.*)", "dest": "/vercel_handler.py"}, {"src": "/docs", "dest": "/vercel_handler.py"}, {"src": "/redoc", "dest": "/vercel_handler.py"}, {"src": "/openapi.json", "dest": "/vercel_handler.py"}, {"src": "/", "dest": "/vercel_handler.py"}, {"src": "/(.*)", "dest": "/vercel_handler.py"}], "env": {"PYTHONPATH": ".", "ENVIRONMENT": "production"}, "functions": {"vercel_handler.py": {"maxDuration": 30, "memory": 1024}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}