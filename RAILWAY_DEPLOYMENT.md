# Railway Deployment Guide (Recommended)

🚀 **Railway is the BEST platform for deploying your UDCPR RAG API**

Railway is specifically designed for applications like yours and provides excellent performance for AI workloads without the limitations of serverless platforms.

## ✅ Why Railway is Perfect for Your API

- **No Timeout Limits**: AI processing can take as long as needed
- **Persistent Containers**: No cold starts, better performance
- **Automatic Scaling**: Handles traffic spikes automatically
- **Built-in Monitoring**: Comprehensive logs and metrics
- **Easy Environment Management**: Simple variable configuration
- **Affordable Pricing**: $5/month for most use cases

## 🚀 Quick Deployment (5 Minutes)

### Step 1: Push to GitHub
```bash
git remote add origin https://github.com/clumsypasta/API-UDCPR.git
git branch -M main
git push -u origin main
```

### Step 2: Deploy to Railway

1. **Go to Railway**
   - Visit [railway.app](https://railway.app)
   - Sign in with GitHub

2. **Create New Project**
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose `clumsypasta/API-UDCPR`

3. **Automatic Detection**
   - Railway automatically detects it's a Python app
   - Uses `railway.toml` configuration (already included)
   - Installs dependencies from `api_requirements.txt`

### Step 3: Set Environment Variables

In Railway dashboard, go to **Variables** tab and add:

```bash
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index
```

Optional variables:
```bash
OPENAI_MODEL=gpt-4o
ENABLE_WEB_SEARCH=false
MAX_HISTORY_MESSAGES=10
TOP_K_RESULTS=5
```

### Step 4: Deploy!

1. Click **"Deploy"**
2. Railway builds and deploys automatically
3. Get your URL: `https://your-app.railway.app`

## 🧪 Test Your Deployment

```bash
# Health check
curl https://your-app.railway.app/api/health

# Chat endpoint
curl -X POST "https://your-app.railway.app/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What are building height restrictions?"}'

# Interactive docs
# Visit: https://your-app.railway.app/docs
```

## 📊 Expected Performance

- **Health Check**: < 1 second
- **Chat Requests**: 2-5 seconds (much faster than Vercel!)
- **Document Search**: 1-3 seconds
- **Cold Starts**: None (persistent containers)

## 🔧 Railway Configuration

Your project includes `railway.toml` with optimal settings:

```toml
[build]
builder = "NIXPACKS"

[deploy]
startCommand = "uvicorn api_server:app --host 0.0.0.0 --port $PORT"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10
```

## 💰 Pricing

- **Hobby Plan**: $5/month
  - 512MB RAM, 1 vCPU
  - Perfect for most use cases
  - Includes custom domain

- **Pro Plan**: $20/month
  - 8GB RAM, 8 vCPU
  - For high-traffic applications

## 📈 Monitoring & Logs

Railway provides excellent monitoring:

1. **Logs**: Real-time application logs
2. **Metrics**: CPU, memory, network usage
3. **Deployments**: Track all deployments
4. **Health Checks**: Automatic health monitoring

## 🔄 Continuous Deployment

Railway automatically redeploys when you push to GitHub:

```bash
# Make changes
git add .
git commit -m "Update API"
git push

# Railway automatically redeploys!
```

## 🌐 Custom Domain (Optional)

1. Go to **Settings** → **Domains**
2. Add your custom domain
3. Update DNS records as shown
4. SSL certificate is automatic

## 🛠️ Advanced Configuration

### Environment-Specific Variables
```bash
# Production optimizations
ENVIRONMENT=production
LOG_LEVEL=INFO
WORKERS=2

# Performance tuning
MAX_TOKENS=800
TEMPERATURE=0.5
```

### Scaling Configuration
Railway automatically scales based on:
- CPU usage
- Memory usage
- Request volume

## 🆘 Troubleshooting

### Build Fails
```bash
# Check railway.toml is present
# Verify api_requirements.txt exists
# Check Railway build logs
```

### App Won't Start
```bash
# Verify environment variables are set
# Check start command in railway.toml
# Review application logs
```

### Slow Performance
```bash
# Check if you're on Hobby plan (upgrade to Pro)
# Monitor resource usage in Railway dashboard
# Optimize your API code
```

## 🎯 Railway vs Other Platforms

| Feature | Railway | Vercel | Render |
|---------|---------|--------|--------|
| **AI Workloads** | ✅ Excellent | ❌ Poor | ✅ Good |
| **Timeout Limits** | ✅ None | ❌ 30s | ✅ 30min |
| **Cold Starts** | ✅ None | ❌ High | ⚠️ Some |
| **Pricing** | ✅ $5/month | ⚠️ Usage-based | ✅ Free tier |
| **Setup Time** | ✅ 5 minutes | ⚠️ Complex | ✅ 10 minutes |
| **Performance** | ✅ Excellent | ❌ Poor | ✅ Good |

## 🎉 Success!

Once deployed on Railway, your API will be:
- **Fast**: 2-5 second response times
- **Reliable**: No timeout issues
- **Scalable**: Automatic scaling
- **Monitored**: Built-in logging and metrics

**Your API endpoints:**
- Main: `https://your-app.railway.app/`
- Health: `https://your-app.railway.app/api/health`
- Chat: `https://your-app.railway.app/api/chat`
- Docs: `https://your-app.railway.app/docs`

## 🔗 Integration

Use your Railway URL in frontend applications:

```javascript
const chatbot = new UDCPRChatbot('https://your-app.railway.app');
const response = await chatbot.sendMessage('What is FSI?');
```

**Railway is the perfect platform for your UDCPR RAG API!** 🚀
