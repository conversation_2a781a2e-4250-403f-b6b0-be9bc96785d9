# UDCPR RAG Chatbot API Documentation

## Overview

The UDCPR RAG Chatbot API is a FastAPI-based REST service that provides intelligent question-answering capabilities for the Unified Development Control and Promotion Regulations (UDCPR) of Maharashtra State, India. It uses Retrieval Augmented Generation (RAG) with OpenAI's GPT models and Pinecone vector database.

## Base URL

```
Production: https://your-domain.com
Development: http://localhost:8000
```

## Authentication

Currently, the API does not require authentication. In production, consider implementing API key authentication.

## API Endpoints

### 1. Health Check

**GET** `/api/health`

Check the health status of the API and its dependencies.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "services": {
    "openai": "connected",
    "pinecone": "connected",
    "web_search": "available"
  },
  "version": "1.0.0",
  "uptime": 3600.5
}
```

### 2. Chat <PERSON>mpletion

**POST** `/api/chat`

Send a message to the chatbot and receive an AI-generated response based on UDCPR documents.

**Request Body:**
```json
{
  "message": "What are the setback requirements for residential buildings?",
  "chat_history": [
    {
      "role": "user",
      "content": "Hello",
      "timestamp": "2024-01-15T10:25:00Z"
    },
    {
      "role": "assistant",
      "content": "Hello! I'm here to help with UDCPR regulations.",
      "timestamp": "2024-01-15T10:25:01Z"
    }
  ],
  "use_web_search": false,
  "session_id": "session_123",
  "max_tokens": 800,
  "temperature": 0.5
}
```

**Parameters:**
- `message` (required): User's question (1-2000 characters)
- `chat_history` (optional): Previous conversation messages
- `use_web_search` (optional): Enable web search for additional context (default: false)
- `session_id` (optional): Session identifier for conversation tracking
- `max_tokens` (optional): Maximum tokens in response (100-2000, default: 800)
- `temperature` (optional): Response creativity (0.0-2.0, default: 0.5)

**Response:**
```json
{
  "response": "According to UDCPR regulations, residential buildings must maintain specific setback requirements...",
  "sources": [
    {
      "id": "chunk_123",
      "score": 0.85,
      "text": "Setback requirements for residential buildings...",
      "metadata": {
        "source": "UDCPR_document.pdf",
        "page": 45,
        "section": "Residential Regulations"
      }
    }
  ],
  "session_id": "session_123",
  "web_search_used": false,
  "web_search_results": null,
  "processing_time": 2.3,
  "token_usage": {
    "prompt_tokens": 150,
    "completion_tokens": 200,
    "total_tokens": 350
  }
}
```

### 3. Document Search

**POST** `/api/search`

Search directly in the UDCPR documents without generating a conversational response.

**Request Body:**
```json
{
  "query": "building height restrictions",
  "top_k": 5,
  "include_metadata": true,
  "min_score": 0.5
}
```

**Parameters:**
- `query` (required): Search query (1-500 characters)
- `top_k` (optional): Number of results to return (1-20, default: 5)
- `include_metadata` (optional): Include document metadata (default: true)
- `min_score` (optional): Minimum relevance score (0.0-1.0, default: 0.0)

**Response:**
```json
{
  "results": [
    {
      "id": "chunk_456",
      "score": 0.92,
      "text": "Building height restrictions in residential zones...",
      "metadata": {
        "source": "UDCPR_document.pdf",
        "page": 67,
        "section": "Height Regulations"
      }
    }
  ],
  "total_results": 1,
  "query": "building height restrictions",
  "processing_time": 0.8
}
```

## Error Responses

All endpoints return errors in the following format:

```json
{
  "detail": "Error message describing what went wrong"
}
```

**Common HTTP Status Codes:**
- `400` - Bad Request (invalid parameters)
- `422` - Validation Error (malformed request body)
- `500` - Internal Server Error
- `503` - Service Unavailable (dependencies not available)

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Default**: 100 requests per hour per IP
- **Headers**: Rate limit information is included in response headers
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for web applications:
- **Development**: All origins allowed (`*`)
- **Production**: Configure specific origins in environment variables

## WebSocket Support

Currently not supported. All interactions are through HTTP REST endpoints.

## SDK and Integration Examples

### JavaScript/TypeScript
```javascript
import { UDCPRChatbot } from './udcpr-chatbot-sdk';

const chatbot = new UDCPRChatbot('https://your-api-domain.com');

// Send a message
const response = await chatbot.sendMessage('What is FSI?', {
  useWebSearch: false
});

console.log(response.response);
```

### Python
```python
import requests

def send_message(message, api_url="http://localhost:8000"):
    response = requests.post(
        f"{api_url}/api/chat",
        json={"message": message}
    )
    return response.json()

result = send_message("What are the parking requirements?")
print(result["response"])
```

### cURL
```bash
curl -X POST "http://localhost:8000/api/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the setback requirements?",
    "use_web_search": false
  }'
```

## Interactive API Documentation

When the API is running, you can access interactive documentation at:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Environment Variables

Required environment variables for the API:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Pinecone Configuration  
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index

# Optional Configuration
ENABLE_WEB_SEARCH=false
SUPABASE_URL=your_supabase_url
SUPABASE_API_KEY=your_supabase_key
```

## Performance Considerations

- **Response Time**: Typical response time is 1-3 seconds
- **Concurrent Requests**: The API can handle multiple concurrent requests
- **Caching**: Consider implementing caching for frequently asked questions
- **Monitoring**: Use the `/api/health` endpoint for monitoring

## Security Considerations

- **API Keys**: Never expose API keys in client-side code
- **Input Validation**: All inputs are validated and sanitized
- **Rate Limiting**: Prevents abuse and ensures fair usage
- **CORS**: Configure appropriate origins for production

## Troubleshooting

### Common Issues

1. **503 Service Unavailable**
   - Check if OpenAI API key is valid
   - Verify Pinecone connection
   - Ensure all required environment variables are set

2. **Slow Response Times**
   - Check OpenAI API status
   - Verify Pinecone index performance
   - Consider reducing `max_tokens` parameter

3. **Empty or Poor Responses**
   - Verify Pinecone index contains data
   - Check if query matches document content
   - Try enabling web search for additional context

### Getting Help

- Check the interactive API docs at `/docs`
- Review server logs for detailed error messages
- Ensure all dependencies are properly configured

## Deployment Platforms

The API is compatible with multiple deployment platforms:

### Recommended Platforms

1. **Railway** - Best for production deployment
   - Automatic scaling
   - Built-in monitoring
   - Easy environment variable management

2. **Render** - Good balance of features and cost
   - Free tier available
   - Automatic deployments from Git

3. **Google Cloud Run** - Serverless container deployment
   - Pay-per-use pricing
   - Automatic scaling to zero

4. **AWS ECS/Fargate** - Enterprise-grade deployment
   - Full AWS integration
   - Advanced monitoring and logging

### Not Recommended

- **Vercel** - Limited support for long-running processes
- **Netlify Functions** - Timeout limitations for AI processing
