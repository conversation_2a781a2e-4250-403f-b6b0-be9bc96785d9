# UDCPR RAG API Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME=UDCPR RAG Chatbot API
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8000

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1024

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index

# RAG Configuration
MAX_HISTORY_MESSAGES=10
TOP_K_RESULTS=5
MIN_RELEVANCE_SCORE=0.5

# Web Search Configuration
ENABLE_WEB_SEARCH=false
WEB_SEARCH_NUM_RESULTS=3

# Supabase Configuration (Optional)
SUPABASE_URL=your_supabase_url_here
SUPABASE_API_KEY=your_supabase_api_key_here

# CORS Configuration
CORS_ORIGINS=*

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
