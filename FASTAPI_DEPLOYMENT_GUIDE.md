# UDCPR RAG API - Complete Deployment Guide

## 🚀 Quick Start

Your UDCPR RAG Chatbot has been converted to a standalone FastAPI server that can be deployed anywhere and integrated with any frontend. This is a much better solution than trying to force Streamlit into Vercel's serverless architecture.

## 📁 New Project Structure

```
your-project/
├── api_server.py              # Main FastAPI application
├── api/
│   ├── __init__.py
│   ├── models.py              # Pydantic models for request/response
│   ├── config.py              # Configuration management
│   └── services/
│       ├── __init__.py
│       ├── rag_service.py     # Core RAG functionality
│       └── legacy_integration.py  # Integration with existing code
├── examples/
│   ├── javascript_integration.js  # JavaScript SDK
│   ├── react_component.jsx       # React component example
│   └── html_example.html         # Complete HTML example
├── api_requirements.txt       # FastAPI dependencies
├── api.env.example           # Environment variables template
├── Dockerfile                # Docker configuration
├── docker-compose.yml        # Local development with Docker
├── railway.toml              # Railway deployment config
├── render_api.yaml           # Render deployment config
└── vercel_api.json           # Vercel deployment config (limited support)
```

## 🛠️ Local Development

### 1. Install Dependencies

```bash
pip install -r api_requirements.txt
```

### 2. Set Environment Variables

Copy `api.env.example` to `.env` and fill in your values:

```bash
cp api.env.example .env
```

Required variables:
```bash
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index
```

### 3. Run the Server

```bash
# Development mode with auto-reload
uvicorn api_server:app --reload --host 0.0.0.0 --port 8000

# Or run directly
python api_server.py
```

### 4. Test the API

- **Interactive Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health
- **Example Frontend**: Open `examples/html_example.html` in your browser

## 🌐 Production Deployment

### Option 1: Railway (Recommended)

Railway is perfect for FastAPI applications and provides excellent performance.

**Steps:**
1. Push your code to GitHub
2. Go to [railway.app](https://railway.app)
3. Connect your GitHub repository
4. Railway will auto-detect the Python app
5. Set environment variables in Railway dashboard
6. Deploy!

**Configuration:**
- Uses `railway.toml` (already created)
- Start command: `uvicorn api_server:app --host 0.0.0.0 --port $PORT`
- Health check: `/api/health`

### Option 2: Render

Great free tier and easy deployment process.

**Steps:**
1. Push your code to GitHub
2. Go to [render.com](https://render.com)
3. Create a new Web Service
4. Connect your GitHub repository
5. Use `render_api.yaml` configuration (already created)
6. Set environment variables in Render dashboard
7. Deploy!

### Option 3: Google Cloud Run

Serverless container deployment with excellent scaling.

**Steps:**
1. Build and push Docker image:
```bash
# Build the image
docker build -t udcpr-rag-api .

# Tag for Google Cloud
docker tag udcpr-rag-api gcr.io/YOUR_PROJECT_ID/udcpr-rag-api

# Push to Google Container Registry
docker push gcr.io/YOUR_PROJECT_ID/udcpr-rag-api
```

2. Deploy to Cloud Run:
```bash
gcloud run deploy udcpr-rag-api \
  --image gcr.io/YOUR_PROJECT_ID/udcpr-rag-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars OPENAI_API_KEY=your_key,PINECONE_API_KEY=your_key
```

### Option 4: AWS ECS/Fargate

Enterprise-grade deployment with full AWS integration.

**Steps:**
1. Create ECR repository
2. Build and push Docker image to ECR
3. Create ECS task definition
4. Create ECS service
5. Configure load balancer
6. Set environment variables

### Option 5: Docker Compose (Self-hosted)

For self-hosted deployment on your own servers.

```bash
# Create .env file with your variables
cp api.env.example .env

# Start the service
docker-compose up -d

# Check logs
docker-compose logs -f
```

## 🔧 Environment Variables

### Required Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=udcpr-rag-index
```

### Optional Variables

```bash
# Application Settings
APP_NAME=UDCPR RAG Chatbot API
DEBUG=false
LOG_LEVEL=INFO

# OpenAI Settings
OPENAI_MODEL=gpt-4o
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# RAG Settings
MAX_HISTORY_MESSAGES=10
TOP_K_RESULTS=5
MIN_RELEVANCE_SCORE=0.5

# Features
ENABLE_WEB_SEARCH=false
SUPABASE_URL=your_supabase_url
SUPABASE_API_KEY=your_supabase_key

# Security
CORS_ORIGINS=*  # Set specific domains in production
```

## 🔗 Frontend Integration

### JavaScript Integration

```javascript
// Import the SDK
import { UDCPRChatbot } from './javascript_integration.js';

// Initialize
const chatbot = new UDCPRChatbot('https://your-api-domain.com');

// Send message
const response = await chatbot.sendMessage('What is FSI?');
console.log(response.response);
```

### React Integration

```jsx
import UDCPRChat from './react_component.jsx';

function App() {
  return (
    <div className="App">
      <UDCPRChat apiBaseUrl="https://your-api-domain.com" />
    </div>
  );
}
```

### HTML Integration

See `examples/html_example.html` for a complete working example.

## 📊 Monitoring and Maintenance

### Health Monitoring

Set up monitoring for the `/api/health` endpoint:

```bash
# Simple health check
curl https://your-api-domain.com/api/health

# Expected response
{
  "status": "healthy",
  "services": {
    "openai": "connected",
    "pinecone": "connected"
  }
}
```

### Logging

The API provides structured logging. In production, consider:
- Centralized logging (e.g., ELK stack, Datadog)
- Log aggregation and analysis
- Error alerting

### Performance Monitoring

Monitor these metrics:
- Response time (should be < 5 seconds)
- Error rate (should be < 1%)
- Token usage (for cost management)
- Concurrent requests

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Set specific CORS origins (not `*`)
- [ ] Implement API key authentication
- [ ] Use HTTPS only
- [ ] Set up rate limiting
- [ ] Monitor for abuse
- [ ] Keep dependencies updated
- [ ] Use environment variables for secrets
- [ ] Implement request logging

### API Key Management

Never expose API keys in:
- Client-side code
- Git repositories
- Public documentation
- Error messages

## 🚫 Why Not Vercel?

While I've provided a Vercel configuration (`vercel_api.json`), **I strongly recommend against using Vercel** for this API because:

1. **Timeout Limitations**: 10-30 second limits (AI processing can take longer)
2. **Cold Starts**: Frequent cold starts affect user experience
3. **Memory Constraints**: Limited memory for AI model operations
4. **Cost**: Can become expensive with high usage
5. **Complexity**: Requires significant workarounds

## ✅ Recommended Deployment Order

1. **Railway** - Best overall experience, great performance
2. **Render** - Good free tier, easy setup
3. **Google Cloud Run** - Excellent for scaling, pay-per-use
4. **AWS ECS** - Enterprise features, full control
5. **Self-hosted Docker** - Maximum control, own infrastructure

## 🆘 Troubleshooting

### Common Issues

**API won't start:**
- Check environment variables are set
- Verify OpenAI and Pinecone API keys
- Check port availability

**Slow responses:**
- Monitor OpenAI API status
- Check Pinecone index performance
- Consider using smaller models for faster responses

**CORS errors:**
- Set appropriate CORS_ORIGINS
- Ensure frontend URL matches CORS configuration

**Memory issues:**
- Increase container memory limits
- Monitor memory usage
- Consider using smaller embedding models

### Getting Help

1. Check the interactive API docs at `/docs`
2. Review application logs
3. Test individual endpoints
4. Verify all environment variables
5. Check external service status (OpenAI, Pinecone)

## 🎉 Success!

Once deployed, your API will be available at:
- **Main endpoint**: `https://your-domain.com/`
- **Chat API**: `https://your-domain.com/api/chat`
- **Health check**: `https://your-domain.com/api/health`
- **Documentation**: `https://your-domain.com/docs`

You can now integrate this API with any frontend, mobile app, or other service!
