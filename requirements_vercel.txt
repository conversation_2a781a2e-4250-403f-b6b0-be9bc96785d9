# Minimal requirements for Vercel deployment
# Keep this as lightweight as possible due to Vercel's size limits

# FastAPI Core
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# Essential AI/ML
openai==1.72.0
pinecone-client==3.0.0
tiktoken==0.7.0

# Utilities
python-dotenv==1.0.1
requests==2.31.0
tenacity==9.0.0

# Optional - comment out if deployment fails due to size
# beautifulsoup4==4.12.2
