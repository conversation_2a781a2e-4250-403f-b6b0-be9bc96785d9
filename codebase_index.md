# RAG System Codebase Index

This document provides an overview of the RAG (Retrieval-Augmented Generation) system codebase, explaining the key components and their interactions.

## Core Components

### 1. Data Processing Pipeline

The data processing pipeline consists of several sequential steps that transform raw documents into searchable vector embeddings:

- **PDF Extraction** (`pdf_extractor.py`): Extracts text from PDF documents with page tracking and metadata
- **Text Extraction** (`text_extractor.py`): Handles extraction from various file formats
- **Text Chunking** (`text_chunker.py`): Splits text into semantic chunks with appropriate overlap
- **Embeddings Generation** (`embeddings_generator.py`): Creates vector embeddings with rate limit handling
- **Pinecone Upload** (`pinecone_uploader.py`): Uploads vectors to Pinecone with metadata

### 2. Query Interface

- **Query Interface** (`query_interface.py`): Provides semantic search functionality against the vector database

### 3. Chatbot Implementation

- **RAG Chatbot** (`rag_chatbot.py`): Implements conversational interface with RAG approach
- **<PERSON><PERSON>** (`chat_handler.py`): Manages chat sessions and interactions
- **Web Search** (`web_search.py`): Provides fallback for questions outside document scope

### 4. User Interfaces

- **Streamlit Web App** (`streamlit_app.py`, `udcpr_chatbot_streamlit.py`): Web interface for the chatbot
- **Command Line Interface** (`main.py`, `app.py`): CLI for running the pipeline and querying

### 5. Persistence and Memory

- **Supabase Configuration** (`supabase_config.py`): Handles chat history persistence

## Workflow Orchestration

### Main Pipeline Script (`main.py`)

Orchestrates the entire RAG pipeline:
1. Extract text from PDF
2. Chunk the text
3. Generate embeddings
4. Upload to Pinecone
5. Provide a query interface

### Process New Files Script (`process_new_files.py`)

Processes additional files for the RAG system:
1. Extract text from various file formats
2. Chunk the text with special handling for tables
3. Generate embeddings
4. Upload to Pinecone

## Key Features

- **Rate Limit Handling**: Implements delays and retries to avoid API rate limits
- **Batch Processing**: Processes data in batches to optimize API calls
- **Token Calculation**: Validates token counts before API calls
- **Checkpointing**: Saves progress to resume long-running processes
- **Error Recovery**: Handles errors gracefully with progress saving
- **Chat Memory**: Maintains conversation context for better responses
- **Web Search Fallback**: Supplements RAG with web search for out-of-domain questions

## Configuration

- **Environment Variables** (`.env`): API keys and configuration settings
- **Requirements** (`requirements.txt`): Python package dependencies

## Execution Scripts

- **run_pipeline.bat**: Runs the full data processing pipeline
- **chat.bat**: Launches the interactive chat interface
- **query.bat**: Runs the query interface for single questions
- **run_web_chatbot.bat**: Starts the Streamlit web interface

## Data Flow

1. Raw documents (PDF, TXT) → Extracted text → Chunked text → Vector embeddings → Pinecone database
2. User query → Query embedding → Vector search → Retrieved context → LLM prompt → Response

## Extension Points

- Add support for more document formats in `text_extractor.py`
- Implement additional vector databases beyond Pinecone
- Enhance chunking strategies in `text_chunker.py`
- Add more sophisticated RAG techniques in `rag_chatbot.py`