"""
UDCPR RAG Chatbot - FastAPI Server
A standalone API server for the UDCPR RAG Chatbot functionality
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Import our custom modules
from api.models import ChatRequest, ChatResponse, SearchRequest, SearchResponse, HealthResponse
from api.services.rag_service import RAGService
from api.config import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global RAG service instance
rag_service: Optional[RAGService] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global rag_service
    
    # Startup
    logger.info("Starting UDCPR RAG API Server...")
    try:
        settings = get_settings()
        rag_service = RAGService(settings)
        await rag_service.initialize()
        logger.info("RAG Service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RAG service: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down UDCPR RAG API Server...")
    if rag_service:
        await rag_service.cleanup()

# Create FastAPI app
app = FastAPI(
    title="UDCPR RAG Chatbot API",
    description="REST API for the Unified Development Control and Promotion Regulations (UDCPR) RAG Chatbot",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_rag_service() -> RAGService:
    """Dependency to get RAG service instance"""
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not initialized")
    return rag_service

@app.get("/", response_model=dict)
async def root():
    """Root endpoint"""
    return {
        "message": "UDCPR RAG Chatbot API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/health"
    }

@app.get("/api/health", response_model=HealthResponse)
async def health_check(service: RAGService = Depends(get_rag_service)):
    """Health check endpoint"""
    try:
        health_status = await service.health_check()
        return HealthResponse(**health_status)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.post("/api/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    service: RAGService = Depends(get_rag_service)
):
    """Main chat endpoint for RAG conversations"""
    try:
        logger.info(f"Chat request received: {request.message[:100]}...")
        
        response = await service.generate_response(
            query=request.message,
            chat_history=request.chat_history or [],
            use_web_search=request.use_web_search,
            session_id=request.session_id
        )
        
        return ChatResponse(**response)
        
    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")

@app.post("/api/search", response_model=SearchResponse)
async def search_documents(
    request: SearchRequest,
    service: RAGService = Depends(get_rag_service)
):
    """Direct document search endpoint"""
    try:
        logger.info(f"Search request received: {request.query[:100]}...")
        
        results = await service.search_documents(
            query=request.query,
            top_k=request.top_k,
            include_metadata=request.include_metadata
        )
        
        return SearchResponse(**results)
        
    except Exception as e:
        logger.error(f"Search request failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search processing failed: {str(e)}")

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    # For local development
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=True,
        log_level="info"
    )
