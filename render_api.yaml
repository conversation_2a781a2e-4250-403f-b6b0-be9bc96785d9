services:
  - type: web
    name: udcpr-rag-api
    env: python
    buildCommand: pip install -r api_requirements.txt
    startCommand: uvicorn api_server:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /api/health
    healthCheckTimeout: 60
    envVars:
      - key: PYTHONUNBUFFERED
        value: true
      - key: ENVIRONMENT
        value: production
      - key: PYTHONPATH
        value: .
      - key: LOG_LEVEL
        value: INFO
      - key: OPENAI_API_KEY
        sync: false
      - key: PINECONE_API_KEY
        sync: false
      - key: PINECONE_ENVIRONMENT
        value: us-east-1
      - key: PINECONE_INDEX_NAME
        value: udcpr-rag-index
      - key: ENABLE_WEB_SEARCH
        value: true
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_API_KEY
        sync: false
