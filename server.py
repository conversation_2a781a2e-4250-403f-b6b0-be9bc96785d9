"""
Simple server for Render deployment

This file provides a simple HTTP server that can be used as an alternative
entry point for Render deployment. It will redirect to the Streamlit app.
"""

import os
import sys
import subprocess
import http.server
import socketserver
from urllib.parse import parse_qs

PORT = int(os.environ.get("PORT", 8000))

class RenderHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'OK')
            return
        
        # Redirect to Streamlit
        self.send_response(302)
        self.send_header('Location', '/')
        self.end_headers()

def run_streamlit():
    """Run the Streamlit app in a subprocess"""
    print("Starting Streamlit app...")
    try:
        # Start Streamlit in a subprocess
        streamlit_process = subprocess.Popen(
            [
                sys.executable, 
                "-m", 
                "streamlit", 
                "run", 
                "app.py", 
                "--server.port=8501", 
                "--server.address=0.0.0.0"
            ],
            env=os.environ.copy()
        )
        return streamlit_process
    except Exception as e:
        print(f"Error starting Streamlit: {e}")
        return None

def main():
    """Main entry point for the server"""
    print(f"Starting server on port {PORT}")
    
    # Start Streamlit
    streamlit_process = run_streamlit()
    
    # Start HTTP server
    with socketserver.TCPServer(("", PORT), RenderHandler) as httpd:
        print(f"Server running at port {PORT}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("Shutting down server...")
            if streamlit_process:
                streamlit_process.terminate()
            httpd.server_close()

if __name__ == "__main__":
    main()
