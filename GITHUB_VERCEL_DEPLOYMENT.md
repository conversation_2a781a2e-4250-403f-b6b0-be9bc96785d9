# GitHub + Vercel Deployment Guide

## 🚨 Important Notice

**Before proceeding with Vercel deployment, please read this:**

Vercel has significant limitations for AI-powered applications like this UDCPR RAG API:
- **30-second timeout limit** (AI processing often takes longer)
- **Cold start delays** affecting user experience
- **Memory constraints** for AI model operations
- **Higher costs** for compute-intensive operations

**We strongly recommend Railway or Render instead** (see alternative options below).

## 📋 Pre-Deployment Checklist

### ✅ Files Ready for GitHub
- [x] `.gitignore` - Excludes sensitive files
- [x] `README_GITHUB.md` - Repository documentation
- [x] `vercel.json` - Vercel configuration
- [x] `vercel_handler.py` - Vercel-specific entry point
- [x] `requirements_vercel.txt` - Minimal dependencies
- [x] `api_server.py` - Main FastAPI application
- [x] `api/` directory - API modules
- [x] `examples/` directory - Integration examples

### ❌ Files Excluded from Git
- `.env` files (contains API keys)
- `__pycache__/` directories
- Virtual environment folders
- Log files and temporary files

## 🚀 Step 1: Push to GitHub

### 1.1 Initialize Git Repository (if not already done)
```bash
git init
git add .
git commit -m "Initial commit: FastAPI UDCPR RAG API"
```

### 1.2 Connect to Your GitHub Repository
```bash
git remote add origin https://github.com/clumsypasta/API-UDCPR.git
git branch -M main
git push -u origin main
```

### 1.3 Verify Repository Contents
After pushing, verify these files are in your GitHub repository:
- `api_server.py`
- `vercel.json`
- `vercel_handler.py`
- `requirements_vercel.txt`
- `api/` folder with all modules
- `examples/` folder
- `README_GITHUB.md`

## 🌐 Step 2: Deploy to Vercel

### 2.1 Connect GitHub to Vercel

1. **Go to Vercel Dashboard**
   - Visit [vercel.com](https://vercel.com)
   - Sign in with your GitHub account

2. **Import Project**
   - Click "New Project"
   - Select "Import Git Repository"
   - Choose `clumsypasta/API-UDCPR`

3. **Configure Project**
   - **Project Name**: `udcpr-rag-api`
   - **Framework Preset**: Other
   - **Root Directory**: `./` (leave as default)
   - **Build Command**: Leave empty (Vercel will auto-detect)
   - **Output Directory**: Leave empty
   - **Install Command**: `pip install -r requirements_vercel.txt`

### 2.2 Set Environment Variables

In Vercel dashboard, go to **Settings → Environment Variables** and add:

#### Required Variables
```
OPENAI_API_KEY = your_openai_api_key_here
PINECONE_API_KEY = your_pinecone_api_key_here
PINECONE_ENVIRONMENT = us-east-1
PINECONE_INDEX_NAME = udcpr-rag-index
```

#### Optional Variables
```
OPENAI_MODEL = gpt-4o
OPENAI_EMBEDDING_MODEL = text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS = 1024
ENABLE_WEB_SEARCH = false
MAX_HISTORY_MESSAGES = 10
TOP_K_RESULTS = 5
MIN_RELEVANCE_SCORE = 0.5
```

### 2.3 Deploy

1. Click **"Deploy"**
2. Wait for build to complete (5-10 minutes)
3. If successful, you'll get a URL like: `https://udcpr-rag-api.vercel.app`

## 🧪 Step 3: Test Deployment

### 3.1 Test Endpoints

```bash
# Health check
curl https://your-app.vercel.app/api/health

# Chat endpoint
curl -X POST "https://your-app.vercel.app/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What are setback requirements?"}'

# Interactive docs
# Visit: https://your-app.vercel.app/docs
```

### 3.2 Expected Response Times
- **Health check**: < 2 seconds
- **Chat requests**: 10-30 seconds (may timeout on Vercel)
- **Document search**: 5-15 seconds

## ⚠️ Common Vercel Issues

### Issue 1: Timeout Errors
**Problem**: Requests timeout after 30 seconds
**Solution**: 
- Reduce `max_tokens` in requests
- Use faster OpenAI models (gpt-3.5-turbo)
- Consider upgrading to Vercel Pro (60-second timeout)

### Issue 2: Cold Start Delays
**Problem**: First request takes very long
**Solution**: 
- Implement warming functions
- Consider serverless alternatives

### Issue 3: Memory Errors
**Problem**: Out of memory during AI processing
**Solution**:
- Reduce batch sizes
- Use smaller embedding models
- Upgrade Vercel plan

### Issue 4: Build Failures
**Problem**: Deployment fails during build
**Solution**:
```bash
# Use minimal requirements
cp requirements_vercel.txt requirements.txt
git add requirements.txt
git commit -m "Use minimal requirements for Vercel"
git push
```

## 🔄 Step 4: Update Deployment

### 4.1 Make Changes
```bash
# Make your changes
git add .
git commit -m "Update API functionality"
git push
```

### 4.2 Automatic Deployment
Vercel will automatically redeploy when you push to the main branch.

## 🎯 Better Alternatives (Recommended)

### Option 1: Railway (Best Choice)

```bash
# 1. Push to GitHub (same as above)
# 2. Go to railway.app
# 3. Connect GitHub repo
# 4. Set environment variables
# 5. Deploy automatically
```

**Advantages:**
- No timeout limitations
- Better performance for AI workloads
- Automatic scaling
- Built-in monitoring

### Option 2: Render

```bash
# 1. Push to GitHub (same as above)
# 2. Go to render.com
# 3. Create new Web Service
# 4. Connect GitHub repo
# 5. Use render_api.yaml configuration
```

**Advantages:**
- Free tier available
- Good performance
- Easy deployment
- Persistent storage options

### Option 3: Google Cloud Run

```bash
# Build and deploy container
docker build -t udcpr-api .
gcloud run deploy --image udcpr-api --platform managed
```

**Advantages:**
- Pay-per-use pricing
- Better timeout limits (up to 60 minutes)
- Automatic scaling
- Enterprise features

## 📊 Deployment Comparison

| Platform | Timeout | Cold Start | Cost | AI Workload | Recommendation |
|----------|---------|------------|------|-------------|----------------|
| **Vercel** | 30s | High | Medium | ❌ Poor | Not Recommended |
| **Railway** | None | Low | Low | ✅ Excellent | **Recommended** |
| **Render** | 30min | Medium | Low | ✅ Good | **Recommended** |
| **Cloud Run** | 60min | Low | Pay-per-use | ✅ Excellent | Good for Scale |

## 🆘 Troubleshooting

### Vercel Deployment Fails
1. Check build logs in Vercel dashboard
2. Verify all environment variables are set
3. Try using `requirements_vercel.txt`
4. Consider switching to Railway/Render

### API Returns Errors
1. Check Vercel function logs
2. Verify OpenAI and Pinecone API keys
3. Test with smaller requests
4. Check timeout settings

### Slow Performance
1. This is expected on Vercel for AI workloads
2. Consider using Railway or Render
3. Optimize model parameters
4. Implement caching

## 🎉 Success Indicators

✅ **Successful Deployment:**
- Health endpoint returns 200 status
- Interactive docs accessible at `/docs`
- Chat endpoint responds (may be slow)
- No build errors in Vercel logs

❌ **Failed Deployment:**
- Build fails with dependency errors
- Timeout errors on API calls
- 500 errors from endpoints
- Memory limit exceeded

## 📞 Next Steps

1. **If Vercel works**: Monitor performance and consider upgrading plan
2. **If Vercel fails**: Switch to Railway or Render (much better for AI APIs)
3. **For production**: Use Railway, Render, or Cloud Run for better reliability

Remember: **Vercel is not optimized for AI workloads**. Railway and Render are much better choices for this type of application.
