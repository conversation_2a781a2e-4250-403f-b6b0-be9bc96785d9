# UDCPR RAG Chatbot - Complete Deployment Instructions

## 🚨 Critical Information

**Your Streamlit app is fundamentally incompatible with Vercel's serverless architecture.** The 404 error you're seeing occurs because:

1. Vercel expects serverless functions or static files
2. Streamlit needs a persistent server process
3. WebSocket connections (used by Streamlit) don't work well with serverless

## ✅ Solution: I've Created Two Deployment Options

### Option 1: Vercel-Compatible Version (Simplified)

I've created a simplified version that works with Vercel but loses Streamlit's interactive features.

**Files Created:**
- `vercel.json` - Vercel configuration
- `api/index.py` - HTML interface serverless function  
- `api/chat.py` - Chat API serverless function
- `index.html` - Static HTML frontend
- `requirements.txt` - Minimal dependencies for Vercel

**Deployment Steps:**

1. **Set Environment Variables in Vercel:**
   - Go to your Vercel project dashboard
   - Navigate to Settings → Environment Variables
   - Add these variables:
     ```
     OPENAI_API_KEY=your_openai_api_key_here
     PINECONE_API_KEY=your_pinecone_api_key_here
     PINECONE_ENVIRONMENT=us-east-1
     ```

2. **Deploy:**
   - Push your updated code to GitHub
   - Vercel will automatically redeploy
   - Access your app at: `https://your-app.vercel.app/`

### Option 2: Full Streamlit Experience (Recommended)

For the complete Streamlit experience with all features, use these platforms:

#### A. Streamlit Cloud (Easiest & Free)

1. **Setup:**
   - Go to [share.streamlit.io](https://share.streamlit.io)
   - Sign in with GitHub
   - Click "New app"
   - Select your repository
   - Set main file path: `chatbot_web.py`

2. **Configure Secrets:**
   - In Streamlit Cloud, go to your app settings
   - Add secrets in TOML format:
     ```toml
     [general]
     OPENAI_API_KEY = "your_openai_api_key_here"
     PINECONE_API_KEY = "your_pinecone_api_key_here"
     PINECONE_ENVIRONMENT = "us-east-1"
     SUPABASE_URL = "your_supabase_url"
     SUPABASE_API_KEY = "your_supabase_key"
     ENABLE_WEB_SEARCH = "true"
     ```

3. **Update Requirements:**
   - Rename `requirements-full.txt` to `requirements.txt`
   - Or copy the contents of `requirements-full.txt` to `requirements.txt`

#### B. Railway (Great Performance)

1. **Setup:**
   - Go to [railway.app](https://railway.app)
   - Connect your GitHub repository
   - Railway auto-detects Python apps

2. **Configure:**
   - Set environment variables in Railway dashboard
   - Use `requirements-full.txt` (rename to `requirements.txt`)
   - Set start command: `streamlit run chatbot_web.py --server.port=$PORT --server.address=0.0.0.0`

#### C. Render (Your Current Config Works)

1. **Setup:**
   - Go to [render.com](https://render.com)
   - Connect your GitHub repository
   - Your existing `render.yaml` will work

2. **Configure:**
   - Set environment variables in Render dashboard
   - Use `requirements-full.txt` (rename to `requirements.txt`)

## 🔧 Quick Fix for Current Vercel Deployment

If you want to try the Vercel version immediately:

1. **Commit and push these new files:**
   ```bash
   git add .
   git commit -m "Add Vercel-compatible API version"
   git push
   ```

2. **Set environment variables in Vercel dashboard**

3. **Wait for automatic deployment**

4. **Test at:** `https://your-app.vercel.app/`

## 📊 Comparison

| Platform | Pros | Cons | Best For |
|----------|------|------|----------|
| **Vercel** | Fast CDN, good for APIs | No Streamlit support | Simple web apps |
| **Streamlit Cloud** | Perfect for Streamlit, free | Limited resources | Streamlit apps |
| **Railway** | Great performance, easy setup | Paid after free tier | Production apps |
| **Render** | Good balance, your config works | Slower cold starts | General hosting |

## 🎯 My Recommendation

**Use Streamlit Cloud for the best experience.** It's:
- Designed specifically for Streamlit apps
- Free for public repositories
- Handles all the Streamlit-specific requirements
- Provides the full interactive experience your users expect

The Vercel version I created is functional but limited compared to your full Streamlit app.

## 🆘 Need Help?

If you encounter issues:

1. **For Vercel version:** Check the browser console for API errors
2. **For Streamlit Cloud:** Check the app logs in the Streamlit Cloud dashboard
3. **For Railway/Render:** Check the deployment logs in their respective dashboards

The most common issue is missing or incorrect environment variables.
